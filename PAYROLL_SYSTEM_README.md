# 💼 Payroll Document Processing System

An intelligent payroll document processor that uses AI to extract structured information from payslips, detect issues, and support HR operations through automated analysis and chatbot integration.

## 🌟 Features

### 📄 Document Processing
- **Multi-format Support**: PDF, images (JPG, PNG), DOCX, DOC, TXT
- **OCR Integration**: DocTR for scanned documents and images
- **Layout-aware Extraction**: Preserves document structure and hierarchy

### 🧠 AI-Powered Analysis
- **Document Classification**: Automatically identifies document types (payslip, offer letter, tax document, etc.)
- **Entity Extraction**: Extracts key payroll fields using SpanMarker NER + regex fallbacks
- **Issue Detection**: Identifies salary discrepancies, missing bonuses, tax errors, and more
- **Complaint Classification**: Analyzes HR complaints and suggests resolutions

### 📊 Structured Data Extraction
- **Employee Information**: ID, name, organization
- **Salary Components**: Gross pay, net pay, basic salary, HRA, bonuses
- **Deductions**: Tax, PF, ESI, professional tax
- **Dates**: Pay period, payment date
- **Validation**: Cross-field validation and anomaly detection

### 🔍 Issue Detection
- Bonus missing
- Tax calculation errors
- Salary delays
- CTC mismatches
- Deduction errors
- Leave balance issues
- Overtime missing
- Allowance discrepancies

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI        │    │   Processing    │
│   (React)       │◄──►│   Backend        │◄──►│   Pipeline      │
│                 │    │                  │    │                 │
│ • Upload UI     │    │ • REST APIs      │    │ • File Processor│
│ • Document View │    │ • Authentication │    │ • OCR (DocTR)   │
│ • Analytics     │    │ • Validation     │    │ • NER (SpanMkr) │
└─────────────────┘    └──────────────────┘    │ • Classification│
                                               │ • Issue Detect  │
                                               └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   Database      │
                                               │   (SQLite)      │
                                               │                 │
                                               │ • Documents     │
                                               │ • Parsed Fields │
                                               │ • Issues        │
                                               └─────────────────┘
```

## 🚀 Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Install spaCy model
python -m spacy download en_core_web_sm
```

### 2. Test the System

```bash
# Run the test suite
python test_payroll_system.py
```

### 3. Start the Server

```bash
# Start FastAPI server
uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### 4. Access the Interface

- **Admin Dashboard**: http://localhost:8000/admin
- **API Documentation**: http://localhost:8000/docs
- **Payroll Manager**: Navigate to the Payroll section in the admin dashboard

## 📚 API Endpoints

### Document Processing

```http
POST /api/payroll/upload
Content-Type: multipart/form-data

# Upload single document
curl -X POST "http://localhost:8000/api/payroll/upload" \
  -F "file=@payslip.pdf" \
  -F "employee_id=TC1024"
```

```http
POST /api/payroll/upload-batch
Content-Type: multipart/form-data

# Upload multiple documents
curl -X POST "http://localhost:8000/api/payroll/upload-batch" \
  -F "files=@payslip1.pdf" \
  -F "files=@payslip2.pdf" \
  -F "employee_ids=TC1024,TC1025"
```

### Complaint Analysis

```http
POST /api/payroll/analyze-complaint
Content-Type: application/json

{
  "complaint_text": "My bonus is missing from this month's payslip",
  "employee_id": "TC1024"
}
```

### Document Search

```http
POST /api/payroll/documents/search
Content-Type: application/json

{
  "employee_id": "TC1024",
  "month": "07",
  "year": "2025",
  "limit": 50
}
```

### Document Details

```http
GET /api/payroll/documents/{document_id}
```

### Statistics

```http
GET /api/payroll/statistics
```

## 🗄️ Database Schema

### Documents Table
```sql
CREATE TABLE documents (
    id INTEGER PRIMARY KEY,
    employee_id TEXT,
    employee_name TEXT,
    organization TEXT,
    month TEXT,
    year TEXT,
    document_type TEXT,
    file_path TEXT,
    file_hash TEXT UNIQUE,
    processing_status TEXT,
    confidence_score REAL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Parsed Fields Table
```sql
CREATE TABLE parsed_fields (
    id INTEGER PRIMARY KEY,
    document_id INTEGER,
    field_name TEXT,
    field_value TEXT,
    confidence REAL,
    extraction_method TEXT,
    flagged BOOLEAN,
    created_at TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents (id)
);
```

### Detected Issues Table
```sql
CREATE TABLE detected_issues (
    id INTEGER PRIMARY KEY,
    document_id INTEGER,
    issue_type TEXT,
    description TEXT,
    confidence REAL,
    status TEXT,
    resolved_by TEXT,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents (id)
);
```

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL=sqlite:///data/db/payroll.db

# Models
NER_MODEL_PATH=data/models/ner_model
CLASSIFIER_MODEL_PATH=data/models/payroll_classifier

# Processing
MAX_FILE_SIZE_MB=20
BATCH_SIZE_LIMIT=20
CONFIDENCE_THRESHOLD=0.5

# OCR
OCR_ENGINE=doctr
OCR_LANGUAGE=en
```

### Model Training

```python
# Train NER model with custom data
from src.ner.payroll_entity_extractor import PayrollEntityExtractor

extractor = PayrollEntityExtractor()
extractor.train_model(
    training_data_path="data/training/entity_training_data.jsonl",
    validation_data_path="data/training/entity_validation_data.jsonl",
    num_epochs=5
)
```

```python
# Train document classifier
from src.document_processing.payroll_classifier import PayrollDocumentClassifier

classifier = PayrollDocumentClassifier()
classifier.train_classifier()  # Uses synthetic data by default
```

## 📊 Supported Document Types

1. **Payslip** - Monthly salary statements
2. **Offer Letter** - Job offer documents
3. **Tax Document** - Form 16, TDS certificates
4. **Bank Statement** - Account statements
5. **Leave Application** - Leave requests
6. **Performance Review** - Appraisal documents
7. **Contract** - Employment agreements
8. **Other** - General HR documents

## 🎯 Extracted Fields

### Personal Information
- Employee ID
- Employee Name
- Organization

### Salary Components
- Gross Pay
- Net Pay
- Basic Salary
- HRA (House Rent Allowance)
- Bonus/Incentives
- Special Allowances

### Deductions
- Income Tax (TDS)
- Provident Fund (PF)
- ESI (Employee State Insurance)
- Professional Tax

### Dates
- Pay Period (Month/Year)
- Payment Date

## ⚠️ Issue Types Detected

1. **bonus_missing** - Missing performance bonuses
2. **tax_error** - Incorrect tax calculations
3. **salary_delay** - Late salary payments
4. **ctc_mismatch** - CTC vs actual salary discrepancies
5. **deduction_error** - Wrong deduction amounts
6. **leave_balance_error** - Incorrect leave calculations
7. **overtime_missing** - Missing overtime payments
8. **allowance_missing** - Missing allowances
9. **payslip_error** - General payslip errors
10. **reimbursement_pending** - Pending expense claims

## 🔮 Future Enhancements

- **Advanced Analytics**: Salary trend analysis, anomaly detection
- **Multi-language Support**: Support for regional languages
- **Integration APIs**: Connect with HRMS, accounting systems
- **Mobile App**: Mobile interface for document upload
- **Blockchain**: Immutable audit trail for payroll records
- **ML Improvements**: Better accuracy with more training data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
- Create an issue on GitHub
- Check the test suite: `python test_payroll_system.py`
- Review API documentation at `/docs`
