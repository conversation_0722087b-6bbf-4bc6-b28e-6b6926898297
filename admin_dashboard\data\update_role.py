import sqlite3

DB_PATH = 'admin_dashboard/data/admin_users.db'  # Corrected path
EMAIL = '<EMAIL>'
NEW_ROLE = 'SUPERADMIN'

def update_role(db_path, email, new_role):
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    cur.execute("UPDATE admin_users SET role=? WHERE email=?", (new_role, email))
    conn.commit()
    print(f"Updated role for {email} to {new_role}")
    conn.close()

if __name__ == '__main__':
    update_role(DB_PATH, EMAIL, NEW_ROLE) 