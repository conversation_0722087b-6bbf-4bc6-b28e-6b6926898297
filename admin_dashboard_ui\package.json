{"name": "admin_dashboard_ui", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.10.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/dompurify": "^3.0.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "ajv": "^8.0.0", "ajv-keywords": "^5.0.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^3.0.0", "dompurify": "^3.0.0", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "i18next": "^23.7.0", "i18next-browser-languagedetector": "^7.2.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.0", "react-helmet-async": "^2.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^14.0.0", "react-intersection-observer": "^9.5.0", "react-is": "^19.1.0", "react-leaflet": "^4.2.1", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^6.30.1", "react-scripts": "^5.0.1", "react-simple-maps": "^3.0.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "recharts": "^3.0.2", "socket.io-client": "^4.7.0", "swr": "^2.3.4", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^4.4.7"}, "proxy": "http://localhost:5052", "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "craco eject", "postinstall": "echo 'WARNING: @ alias will not work unless craco.config.js is set up. Use relative imports or add craco.config.js with alias config.'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.29", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.4"}}