import React from "react";
import { useAuthStore } from "../hooks/useAuthStore";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { User, Settings, LogOut, Shield } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export const UserProfile = () => {
  const { role, setRole, logout, user } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const getRoleColor = (userRole: string) => {
    return userRole === "SUPERADMIN" 
      ? "bg-blue-600 text-white" 
      : "bg-green-600 text-white";
  };

  const getRoleIcon = (userRole: string) => {
    return userRole === "SUPERADMIN" ? <Shield className="h-3 w-3" /> : <User className="h-3 w-3" />;
  };

  const getUserInitials = () => {
    const displayName = user?.name || user?.full_name || user?.email || '';
    if (displayName) {
      return displayName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return '';
  };

  const getUserEmail = () => {
    return user?.email || '';
  };

  const getUserName = () => {
    return user?.name || user?.full_name || user?.email || '';
  };

  return (
    <div className="flex items-center gap-3">
      {/* Enhanced Role Badge with Tooltip and Glow */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={`flex items-center gap-1 px-4 py-1.5 rounded-full text-sm font-bold shadow-md transition-all duration-200
                ${role === "SUPERADMIN"
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white ring-2 ring-blue-400/60 animate-pulse"
                  : "bg-green-600 text-white"}
                cursor-default select-none relative`}
            >
              {getRoleIcon(role || '')}
              {role === "SUPERADMIN" ? "Superadmin" : role === "VIEWER" ? "Viewer" : role || 'Unknown'}
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom" align="center">
            {role === "SUPERADMIN"
              ? "Superadmin: Full access"
              : role === "VIEWER"
              ? "Viewer: Read-only access"
              : role || 'Unknown'}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* User Profile Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="relative">
            <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
              {getUserInitials()}
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {getUserName() || 'Unknown'}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {getUserEmail() || 'Unknown'}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}; 