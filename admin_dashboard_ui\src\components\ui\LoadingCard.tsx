import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface LoadingCardProps {
  className?: string;
  shimmer?: boolean;
  lines?: number;
}

export const LoadingCard: React.FC<LoadingCardProps> = ({ 
  className, 
  shimmer = true, 
  lines = 3 
}) => {
  return (
    <div className={cn(
      "rounded-xl border bg-card p-6 shadow-sm",
      "bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50",
      className
    )}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="space-y-4">
          {/* Header skeleton */}
          <div className="flex items-center gap-3">
            <div className={cn(
              "h-10 w-10 rounded-lg bg-muted animate-pulse",
              shimmer && "relative overflow-hidden",
              shimmer && "before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent"
            )} />
            <div className="space-y-2 flex-1">
              <div className={cn(
                "h-4 w-24 rounded bg-muted animate-pulse",
                shimmer && "relative overflow-hidden",
                shimmer && "before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent"
              )} />
              <div className={cn(
                "h-6 w-16 rounded bg-muted animate-pulse",
                shimmer && "relative overflow-hidden",
                shimmer && "before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent"
              )} />
            </div>
          </div>
          
          {/* Content lines */}
          <div className="space-y-2">
            {Array.from({ length: lines }).map((_, i) => (
              <div
                key={i}
                className={cn(
                  "h-3 rounded bg-muted animate-pulse",
                  i === lines - 1 ? "w-2/3" : "w-full",
                  shimmer && "relative overflow-hidden",
                  shimmer && "before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent"
                )}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

interface LoadingChartProps {
  className?: string;
  height?: string;
}

export const LoadingChart: React.FC<LoadingChartProps> = ({ 
  className, 
  height = "h-80" 
}) => {
  return (
    <div className={cn(
      "rounded-xl border bg-card p-6 shadow-sm",
      "bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50",
      height,
      className
    )}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="h-full flex flex-col space-y-4">
          {/* Chart title skeleton */}
          <div className="flex items-center justify-between">
            <div className="h-6 w-32 rounded bg-muted animate-pulse relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent" />
            <div className="h-8 w-20 rounded bg-muted animate-pulse relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent" />
          </div>
          
          {/* Chart area skeleton */}
          <div className="flex-1 flex items-end justify-between space-x-2">
            {Array.from({ length: 8 }).map((_, i) => (
              <div
                key={i}
                className="bg-muted animate-pulse rounded-t relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent"
                style={{ 
                  height: `${Math.random() * 60 + 20}%`,
                  width: '12%',
                  animationDelay: `${i * 0.1}s`
                }}
              />
            ))}
          </div>
          
          {/* Legend skeleton */}
          <div className="flex justify-center space-x-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-2">
                <div className="h-3 w-3 rounded-full bg-muted animate-pulse" />
                <div className="h-3 w-16 rounded bg-muted animate-pulse relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent" />
              </div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
};
