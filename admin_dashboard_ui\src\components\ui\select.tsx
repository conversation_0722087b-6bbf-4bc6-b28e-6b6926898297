import React from "react";

type SelectProps = React.SelectHTMLAttributes<HTMLSelectElement> & {
  onValueChange?: (value: string) => void;
};

export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ value, onChange, onValueChange, children, ...props }, ref) => (
    <select
      ref={ref}
      value={value}
      onChange={e => {
        onChange?.(e);
        onValueChange?.(e.target.value);
      }}
      {...props}
    >
      {children}
    </select>
  )
);
Select.displayName = 'Select'; 