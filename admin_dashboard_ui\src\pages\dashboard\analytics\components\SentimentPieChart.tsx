import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = {
  positive: "#10b981",
  neutral: "#f59e42",
  negative: "#ef4444"
};

const SENTIMENT_COLORS = ["#10b981", "#f59e42", "#ef4444"];

interface SentimentPieChartProps {
  sentimentDistribution: any[];
  loading: boolean;
  error?: string | Error | null;
  height?: number;
}

const SentimentPieChart: React.FC<SentimentPieChartProps> = ({ sentimentDistribution, loading, error, height = 224 }) => {
  if (loading) return <Skeleton className={`w-full`} shimmer style={{height}} />;
  if (error) return <div className="text-red-500 p-4">{typeof error === 'string' ? error : error?.message}</div>;
  if (!sentimentDistribution || !sentimentDistribution.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background/95 backdrop-blur-sm border border-border/50 rounded-xl shadow-lg p-4 text-sm">
          <div className="font-semibold mb-1 capitalize">{data.sentiment} Sentiment</div>
          <div className="text-muted-foreground">
            Count: <span className="font-medium text-foreground">{data.count}</span>
          </div>
          <div className="text-muted-foreground">
            Percentage: <span className="font-medium text-foreground">{data.percentage}%</span>
          </div>
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent === 0) return null;
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="black"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        className="text-sm font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

// Custom legend component
const SentimentLegend: React.FC<{ data: any[] }> = ({ data }) => (
  <div className="flex flex-col gap-2 ml-8">
    {data.map((entry, idx) => (
      <div key={entry.sentiment} className="flex items-center gap-2">
        <span style={{ backgroundColor: SENTIMENT_COLORS[idx % SENTIMENT_COLORS.length], width: 16, height: 16, display: 'inline-block', borderRadius: 4 }} />
        <span className="text-sm" style={{ color: 'hsl(var(--muted-foreground))' }}>{entry.sentiment}</span>
      </div>
    ))}
  </div>
);

  return (
    <div className="w-full rounded-xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 p-4 flex justify-center items-center" style={{height}}>
      <div className="flex w-full items-center justify-center" style={{height: '100%'}}>
        <div className="flex-shrink-0 flex-grow-0" style={{width: 260, height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
          <ResponsiveContainer width={220} height={height ? height - 32 : 180}>
            <PieChart>
              <Pie
                data={sentimentDistribution}
                dataKey="count"
                nameKey="sentiment"
                cx="50%"
                cy="50%"
                labelLine={false}
                label={CustomLabel}
                outerRadius={65}
                innerRadius={40}
                fill="#8884d8"
                stroke="none"
              >
                {sentimentDistribution.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={SENTIMENT_COLORS[index % SENTIMENT_COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <SentimentLegend data={sentimentDistribution} />
      </div>
    </div>
  );
};

export default SentimentPieChart; 