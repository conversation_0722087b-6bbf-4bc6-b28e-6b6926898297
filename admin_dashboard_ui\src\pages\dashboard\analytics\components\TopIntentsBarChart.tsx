import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

interface TopIntentsBarChartProps {
  topIntents: any[];
  loading: boolean;
  error?: string | Error | null;
  height?: number;
}

const TopIntentsBarChart: React.FC<TopIntentsBarChartProps> = ({ topIntents, loading, error, height = 224 }) => {
  if (loading) return <Skeleton className={`w-full`} style={{height}} />;
  if (error) return <div className="text-red-500 p-4">{typeof error === 'string' ? error : error?.message}</div>;
  // Sanitize and filter data: trim intent, remove empty/null, and deduplicate
  const sanitizedIntents = Array.from(
    new Map(
      (topIntents || [])
        .map((item) => ({
          ...item,
          intent: typeof item.intent === 'string' ? item.intent.trim() : ''
        }))
        .filter((item) => item.intent)
        .map((item) => [item.intent, item]) // deduplicate by intent
    ).values()
  );
  if (!sanitizedIntents.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  return (
    <div className="w-full rounded-xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 p-4" style={{height}}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={sanitizedIntents}
          layout="vertical"
          margin={{ top: 20, right: 20, left: 20, bottom: 20 }}
        >
          <CartesianGrid
            strokeDasharray="3 3"
            stroke="hsl(var(--muted-foreground))"
            opacity={0.3}
            horizontal={false}
          />
          <XAxis
            type="number"
            stroke="hsl(var(--muted-foreground))"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            allowDecimals={false}
            tickFormatter={(value) => Number.isInteger(value) ? value : ''}
          />
          <YAxis
            dataKey="intent"
            type="category"
            stroke="hsl(var(--muted-foreground))"
            width={200}
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            content={({ active, payload }) => {
              if (active && payload && payload.length) {
                const { intent, samples, count } = payload[0].payload;
                return (
                  <div className="bg-background/95 backdrop-blur-sm dark:bg-zinc-900/95 border border-border/50 dark:border-zinc-800/50 rounded-xl shadow-lg p-4 text-sm max-w-xs">
                    <div className="font-semibold mb-2 text-foreground">{intent}</div>
                    <div className="mb-2 text-blue-600 font-bold">Count: {count}</div>
                    {samples && samples.length > 0 && (
                      <>
                        <div className="text-muted-foreground mb-1">Most frequent questions:</div>
                        <ul className="list-disc ml-4 space-y-1">
                          {samples.slice(0, 3).map((q: string, i: number) => (
                            <li key={i} className="text-xs text-muted-foreground">{q}</li>
                          ))}
                        </ul>
                      </>
                    )}
                  </div>
                );
              }
              return null;
            }}
          />
          <Bar
            dataKey="count"
            fill="url(#colorGradient)"
            name="Frequency"
            radius={[0, 6, 6, 0]}
          />
          <defs>
            <linearGradient id="colorGradient" x1="0" y1="0" x2="1" y2="0">
              <stop offset="0%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#1d4ed8" />
            </linearGradient>
          </defs>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TopIntentsBarChart; 