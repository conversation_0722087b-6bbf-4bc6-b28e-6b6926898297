import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>spons<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#6366f1", "#10b981", "#f59e42", "#ef4444", "#6366a1", "#f472b6", "#fbbf24", "#0ea5e9", "#a3e635", "#f87171"];

interface TopicTrendsLineChartProps {
  trendingTopics: Array<{ topic: string; trend: number[] }>;
  topics: string[];
  loading?: boolean;
  error?: string | null;
}

const TopicTrendsLineChart: React.FC<TopicTrendsLineChartProps> = ({ trendingTopics, topics, loading, error }) => {
  if (loading) return <Skeleton className="h-56 w-full" shimmer />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!trendingTopics || !trendingTopics.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  // Transform trendingTopics to chart data: [{date: 'Day 1', topic1: val, topic2: val, ...}, ...]
  const maxLen = Math.max(...trendingTopics.map(t => t.trend.length));
  const chartData = Array.from({ length: maxLen }).map((_, i) => {
    const entry: any = { date: `Day ${i + 1}` };
    trendingTopics.forEach(t => {
      entry[t.topic] = t.trend[i] ?? null;
    });
    return entry;
  });

  return (
    <div className="h-56 w-full rounded-xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 p-4">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 20, right: 20, left: 20, bottom: 20 }}>
          <CartesianGrid
            strokeDasharray="3 3"
            stroke="hsl(var(--muted-foreground))"
            opacity={0.3}
          />
          <XAxis
            dataKey="date"
            stroke="hsl(var(--muted-foreground))"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            stroke="hsl(var(--muted-foreground))"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            content={({ active, payload, label }) => {
              if (active && payload && payload.length) {
                return (
                  <div className="bg-background/95 backdrop-blur-sm border border-border/50 rounded-xl shadow-lg p-4 text-sm">
                    <div className="font-semibold mb-2 text-foreground">{label}</div>
                    <div className="space-y-1">
                      {payload.map((p: any, i: number) => (
                        <div key={i} className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: p.color }}
                          />
                          <span className="text-muted-foreground">{p.name}:</span>
                          <span className="font-medium text-foreground">{p.value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              }
              return null;
            }}
          />
          <Legend
            verticalAlign="bottom"
            height={36}
            wrapperStyle={{
              fontSize: '12px',
              color: 'hsl(var(--muted-foreground))'
            }}
          />
          {topics.map((topic, idx) => (
            <Line
              key={topic}
              type="monotone"
              dataKey={topic}
              stroke={COLORS[idx % COLORS.length]}
              strokeWidth={3}
              dot={{ r: 4, strokeWidth: 2, fill: COLORS[idx % COLORS.length] }}
              activeDot={{ r: 6, strokeWidth: 2, fill: COLORS[idx % COLORS.length] }}
              name={topic}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TopicTrendsLineChart; 