import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select"; // Assume stub
import api from "@/services/api";

const ESCALATION_TYPES = ["All", "Policy", "Technical", "HR", "Other"];
const AGENTS = ["All", "Agent A", "Agent B", "Agent C"];
const STATUS = ["All", "Unresolved", "Delayed", "Resolved"];

const columns = [
  { key: "user_email", label: "User Email" },
  { key: "topic", label: "Topic" },
  { key: "timestamp", label: "Timestamp" },
  { key: "resolved_by", label: "Resolved By" },
  { key: "resolution_time", label: "Resolution Time" },
];

const EscalatedChats = () => {
  const [escalationType, setEscalationType] = useState("All");
  const [agent, setAgent] = useState("All");
  const [status, setStatus] = useState("All");
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      escalationType !== "All" ? `type=${encodeURIComponent(escalationType)}` : null,
      agent !== "All" ? `agent=${encodeURIComponent(agent)}` : null,
      status !== "All" ? `status=${encodeURIComponent(status)}` : null,
    ]
      .filter(Boolean)
      .join("&");
    api
      .get(`/feedback/escalations${params ? `?${params}` : ""}`)
      .then((res) => setData(res.data))
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [escalationType, agent, status]);

  const handleExportCSV = () => {
    alert("Export to CSV (mock)");
  };
  const handleExportPDF = () => {
    alert("Export to PDF (mock)");
  };

  return (
    <div className="max-w-6xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>Escalated Chats</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Select value={status} onValueChange={setStatus}>
              {STATUS.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Select value={agent} onValueChange={setAgent}>
              {AGENTS.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Select value={escalationType} onValueChange={setEscalationType}>
              {ESCALATION_TYPES.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Button size="sm" variant="outline" onClick={handleExportCSV}>Export CSV</Button>
            <Button size="sm" variant="outline" onClick={handleExportPDF}>Export PDF</Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-80 w-full" />
          ) : error ? (
            <div className="text-red-500 p-4">{error}</div>
          ) : !data.length ? (
            <div className="text-muted-foreground p-4">No data available.</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
                <thead className="bg-muted dark:bg-zinc-800">
                  <tr>
                    {columns.map(col => (
                      <th key={col.key} className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">
                        {col.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-background dark:bg-zinc-900">
                  {data.map((row, idx) => (
                    <tr key={idx} className="border-b border-border dark:border-zinc-800">
                      {columns.map(col => (
                        <td key={col.key} className="px-4 py-2 text-sm">
                          {row[col.key]}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EscalatedChats; 