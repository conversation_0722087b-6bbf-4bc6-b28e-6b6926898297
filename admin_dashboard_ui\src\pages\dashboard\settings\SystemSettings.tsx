import React, { useState } from "react";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { <PERSON>, CardHeader, <PERSON>T<PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { Toggle } from "@/components/ui/toggle";
import { useThemeStore } from "@/hooks/useThemeStore";

const FREQUENCIES = ["Daily", "Weekly", "Monthly"];

const SystemSettings: React.FC = () => {
  // Email Scheduler
  const [frequency, setFrequency] = useState("Weekly");
  const [time, setTime] = useState("09:00");
  const [recipients, setRecipients] = useState("");
  const [saving, setSaving] = useState(false);

  // Notifications
  const [systemAlerts, setSystemAlerts] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);

  // Privacy & Security
  const [autoLogout, setAutoLogout] = useState(false);
  const [enable2FA, setEnable2FA] = useState(false);

  // Theme
  const theme = useThemeStore((s) => s.theme);
  const setThemeMode = useThemeStore((s) => s.setThemeMode);

  // Handlers
  const handleSave = () => {
    setSaving(true);
    setTimeout(() => {
      setSaving(false);
      alert("Settings saved (stub)");
    }, 800);
  };
  const handleExportPDF = () => alert("Export PDF (stub)");
  const handleExportCSV = () => alert("Export CSV (stub)");
  const handleManageDevices = () => alert("Manage trusted devices (stub)");

  // Validation
  const isValidEmails = (emails: string) => {
    if (!emails.trim()) return true;
    return emails.split(",").every(email => email.trim().match(/^\S+@\S+\.\S+$/));
  };

  return (
    <div className="max-w-3xl mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">System Settings</h1>
      <Accordion type="multiple" className="space-y-4">
        {/* Appearance */}
        <AccordionItem value="appearance">
          <AccordionTrigger>Appearance</AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardHeader>
                <CardTitle>Appearance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col gap-4">
                  <div className="flex items-center gap-4">
                    <span className="text-sm font-semibold">Theme</span>
                    <div className="flex gap-2">
                      <Button
                        variant={theme === "light" ? "default" : "outline"}
                        onClick={() => setThemeMode("light")}
                        aria-pressed={theme === "light"}
                      >
                        Light
                      </Button>
                      <Button
                        variant={theme === "dark" ? "default" : "outline"}
                        onClick={() => setThemeMode("dark")}
                        aria-pressed={theme === "dark"}
                      >
                        Dark
                      </Button>
                      <Button
                        variant={theme === "system" ? "default" : "outline"}
                        onClick={() => setThemeMode("system")}
                        aria-pressed={theme === "system"}
                      >
                        System
                      </Button>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Current: <span className="font-semibold">{theme.charAt(0).toUpperCase() + theme.slice(1)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
        {/* Email Scheduler */}
        <AccordionItem value="email-scheduler">
          <AccordionTrigger>Email Scheduler</AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardHeader>
                <CardTitle>Email Scheduler</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col md:flex-row gap-4 items-center">
                  <span className="text-sm font-semibold">Frequency</span>
                  <Select value={frequency} onValueChange={setFrequency} className="bg-white text-gray-900 dark:bg-neutral-800 dark:text-neutral-100">
                    {FREQUENCIES.map(opt => (
                      <option key={opt} value={opt}>{opt}</option>
                    ))}
                  </Select>
                  <Input type="time" value={time} onChange={e => setTime(e.target.value)} className="w-28 bg-white text-gray-900 dark:bg-neutral-800 dark:text-neutral-100 placeholder:dark:text-neutral-400" />
                  <Input
                    type="text"
                    placeholder="Recipients (comma-separated emails)"
                    value={recipients}
                    onChange={e => setRecipients(e.target.value)}
                    className="w-64 bg-white text-gray-900 dark:bg-neutral-800 dark:text-neutral-100 placeholder:dark:text-neutral-400"
                  />
                  <Button size="sm" variant="default" onClick={handleSave} disabled={saving || !isValidEmails(recipients)} className="bg-blue-500 text-white dark:bg-blue-600 dark:text-white">
                    {saving ? "Saving..." : "Save"}
                  </Button>
                </div>
                {!isValidEmails(recipients) && (
                  <div className="text-xs text-red-500">Invalid email(s) format</div>
                )}
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={handleExportPDF} className="bg-neutral-900 text-white border-neutral-700 dark:bg-neutral-800 dark:text-neutral-100 dark:border-neutral-700">Export PDF</Button>
                  <Button size="sm" variant="outline" onClick={handleExportCSV} className="bg-neutral-900 text-white border-neutral-700 dark:bg-neutral-800 dark:text-neutral-100 dark:border-neutral-700">Export CSV</Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
        {/* Notifications */}
        <AccordionItem value="notifications">
          <AccordionTrigger>Notifications</AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-semibold">Enable System Alerts</span>
                  <Toggle checked={systemAlerts} onCheckedChange={() => setSystemAlerts(v => !v)}>
                    {systemAlerts ? "On" : "Off"}
                  </Toggle>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm font-semibold">Enable Email Notifications</span>
                  <Toggle checked={emailNotifications} onCheckedChange={() => setEmailNotifications(v => !v)}>
                    {emailNotifications ? "On" : "Off"}
                  </Toggle>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
        {/* Privacy & Security */}
        <AccordionItem value="privacy-security">
          <AccordionTrigger>Privacy & Security</AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardHeader>
                <CardTitle>Privacy & Security</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-semibold">Auto Logout After Inactivity</span>
                  <Toggle checked={autoLogout} onCheckedChange={() => setAutoLogout(v => !v)}>
                    {autoLogout ? "On" : "Off"}
                  </Toggle>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm font-semibold">Enable 2FA for Admin Accounts</span>
                  <Toggle checked={enable2FA} onCheckedChange={() => setEnable2FA(v => !v)}>
                    {enable2FA ? "On" : "Off"}
                  </Toggle>
                </div>
                <Button size="sm" variant="outline" onClick={handleManageDevices}>Manage Trusted Devices</Button>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default SystemSettings; 