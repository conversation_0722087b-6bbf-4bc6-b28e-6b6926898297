{"architectures": ["SpanMarkerModel"], "encoder": {"_attn_implementation_autoset": false, "_name_or_path": "microsoft/deberta-v3-base", "add_cross_attention": false, "architectures": null, "attention_probs_dropout_prob": 0.1, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "id2label": {"0": "O", "1": "BENEFIT_TYPE", "2": "DATE", "3": "DEPARTMENT", "4": "DOCUMENT_TYPE", "5": "EMPLOYEE_NAME", "6": "HR_EVENT", "7": "HR_PROCESS", "8": "LEAVE_BALANCE", "9": "LEAVE_TYPE", "10": "MONTH", "11": "POLICY_NAME", "12": "YEAR", "13": "amount", "14": "download_payslip", "15": "expense_type", "16": "intent_routing", "17": "issue_type", "18": "ner", "19": "payslip_context", "20": "salary_component", "21": "salary_type", "22": "summarization", "23": "text_extraction"}, "initializer_range": 0.02, "intermediate_size": 3072, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"BENEFIT_TYPE": 1, "DATE": 2, "DEPARTMENT": 3, "DOCUMENT_TYPE": 4, "EMPLOYEE_NAME": 5, "HR_EVENT": 6, "HR_PROCESS": 7, "LEAVE_BALANCE": 8, "LEAVE_TYPE": 9, "MONTH": 10, "O": 0, "POLICY_NAME": 11, "YEAR": 12, "amount": 13, "download_payslip": 14, "expense_type": 15, "intent_routing": 16, "issue_type": 17, "ner": 18, "payslip_context": 19, "salary_component": 20, "salary_type": 21, "summarization": 22, "text_extraction": 23}, "layer_norm_eps": 1e-07, "legacy": true, "length_penalty": 1.0, "max_length": 20, "max_position_embeddings": 512, "max_relative_positions": -1, "min_length": 0, "model_type": "deberta-v2", "no_repeat_ngram_size": 0, "norm_rel_ebd": "layer_norm", "num_attention_heads": 12, "num_beam_groups": 1, "num_beams": 1, "num_hidden_layers": 12, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 0, "pooler_dropout": 0, "pooler_hidden_act": "gelu", "pooler_hidden_size": 768, "pos_att_type": ["p2c", "c2p"], "position_biased_input": false, "position_buckets": 256, "prefix": null, "problem_type": null, "pruned_heads": {}, "relative_attention": true, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "share_att_key": true, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "transformers_version": "4.49.0", "type_vocab_size": 0, "typical_p": 1.0, "use_bfloat16": false, "vocab_size": 128008}, "entity_max_length": 8, "marker_max_length": 128, "max_next_context": null, "max_prev_context": null, "model_max_length": null, "model_max_length_default": 512, "model_type": "span-marker", "span_marker_version": "1.7.0", "torch_dtype": "float32", "trained_with_document_context": false, "transformers_version": "4.49.0", "vocab_size": 128008}