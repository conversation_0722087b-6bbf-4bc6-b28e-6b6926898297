{"version": 3, "sources": ["../../micromark-util-symbol/lib/codes.js", "../../micromark-util-symbol/lib/constants.js", "../../micromark-util-symbol/lib/types.js", "../../micromark-util-symbol/lib/values.js", "../../micromark-util-chunked/dev/index.js", "../../micromark-util-combine-extensions/index.js", "../../mdast-util-to-string/lib/index.js", "../../decode-named-character-reference/index.dom.js", "../../micromark-util-decode-numeric-character-reference/dev/index.js", "../../micromark-util-normalize-identifier/dev/index.js", "../../micromark-util-character/dev/index.js", "../../micromark-util-sanitize-uri/dev/index.js", "../../micromark-factory-space/dev/index.js", "../../micromark-util-classify-character/dev/index.js", "../../micromark-util-resolve-all/index.js", "../../micromark-core-commonmark/dev/lib/blank-line.js", "../../micromark-core-commonmark/dev/lib/attention.js", "../../micromark-core-commonmark/dev/lib/autolink.js", "../../micromark-core-commonmark/dev/lib/block-quote.js", "../../micromark-core-commonmark/dev/lib/character-escape.js", "../../micromark-core-commonmark/dev/lib/character-reference.js", "../../micromark-core-commonmark/dev/lib/code-fenced.js", "../../micromark-core-commonmark/dev/lib/code-indented.js", "../../micromark-core-commonmark/dev/lib/code-text.js", "../../micromark-util-subtokenize/dev/lib/splice-buffer.js", "../../micromark-util-subtokenize/dev/index.js", "../../micromark-core-commonmark/dev/lib/content.js", "../../micromark-factory-destination/dev/index.js", "../../micromark-factory-label/dev/index.js", "../../micromark-factory-title/dev/index.js", "../../micromark-factory-whitespace/dev/index.js", "../../micromark-core-commonmark/dev/lib/definition.js", "../../micromark-core-commonmark/dev/lib/hard-break-escape.js", "../../micromark-core-commonmark/dev/lib/heading-atx.js", "../../micromark-util-html-tag-name/index.js", "../../micromark-core-commonmark/dev/lib/html-flow.js", "../../micromark-core-commonmark/dev/lib/html-text.js", "../../micromark-core-commonmark/dev/lib/label-end.js", "../../micromark-core-commonmark/dev/lib/label-start-image.js", "../../micromark-core-commonmark/dev/lib/label-start-link.js", "../../micromark-core-commonmark/dev/lib/line-ending.js", "../../micromark-core-commonmark/dev/lib/thematic-break.js", "../../micromark-core-commonmark/dev/lib/list.js", "../../micromark-core-commonmark/dev/lib/setext-underline.js", "../../micromark-util-decode-string/dev/index.js"], "sourcesContent": ["/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n", "/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n", "/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nexport const types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n", "/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n", "import {constants} from 'micromark-util-symbol'\n\n/**\n * Like `Array#splice`, but smarter for giant arrays.\n *\n * `Array#splice` takes all items to be inserted as individual argument which\n * causes a stack overflow in V8 when trying to insert 100k items for instance.\n *\n * Otherwise, this does not return the removed items, and takes `items` as an\n * array instead of rest parameters.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {number} start\n *   Index to remove/insert at (can be negative).\n * @param {number} remove\n *   Number of items to remove.\n * @param {Array<T>} items\n *   Items to inject into `list`.\n * @returns {undefined}\n *   Nothing.\n */\nexport function splice(list, start, remove, items) {\n  const end = list.length\n  let chunkStart = 0\n  /** @type {Array<unknown>} */\n  let parameters\n\n  // Make start between zero and `end` (included).\n  if (start < 0) {\n    start = -start > end ? 0 : end + start\n  } else {\n    start = start > end ? end : start\n  }\n\n  remove = remove > 0 ? remove : 0\n\n  // No need to chunk the items if there’s only a couple (10k) items.\n  if (items.length < constants.v8MaxSafeChunkSize) {\n    parameters = Array.from(items)\n    parameters.unshift(start, remove)\n    // @ts-expect-error Hush, it’s fine.\n    list.splice(...parameters)\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) list.splice(start, remove)\n\n    // Insert the items in chunks to not cause stack overflows.\n    while (chunkStart < items.length) {\n      parameters = items.slice(\n        chunkStart,\n        chunkStart + constants.v8MaxSafeChunkSize\n      )\n      parameters.unshift(start, 0)\n      // @ts-expect-error Hush, it’s fine.\n      list.splice(...parameters)\n\n      chunkStart += constants.v8MaxSafeChunkSize\n      start += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n\n/**\n * Append `items` (an array) at the end of `list` (another array).\n * When `list` was empty, returns `items` instead.\n *\n * This prevents a potentially expensive operation when `list` is empty,\n * and adds items in batches to prevent V8 from hanging.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {Array<T>} items\n *   Items to add to `list`.\n * @returns {Array<T>}\n *   Either `list` or `items`.\n */\nexport function push(list, items) {\n  if (list.length > 0) {\n    splice(list, list.length, 0, items)\n    return list\n  }\n\n  return items\n}\n", "/**\n * @import {\n *   Extension,\n *   Handles,\n *   HtmlExtension,\n *   NormalizedExtension\n * } from 'micromark-util-types'\n */\n\nimport {splice} from 'micromark-util-chunked'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * Combine multiple syntax extensions into one.\n *\n * @param {ReadonlyArray<Extension>} extensions\n *   List of syntax extensions.\n * @returns {NormalizedExtension}\n *   A single combined extension.\n */\nexport function combineExtensions(extensions) {\n  /** @type {NormalizedExtension} */\n  const all = {}\n  let index = -1\n\n  while (++index < extensions.length) {\n    syntaxExtension(all, extensions[index])\n  }\n\n  return all\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {NormalizedExtension} all\n *   Extension to merge into.\n * @param {Extension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction syntaxExtension(all, extension) {\n  /** @type {keyof Extension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    /** @type {Record<string, unknown>} */\n    const left = maybe || (all[hook] = {})\n    /** @type {Record<string, unknown> | undefined} */\n    const right = extension[hook]\n    /** @type {string} */\n    let code\n\n    if (right) {\n      for (code in right) {\n        if (!hasOwnProperty.call(left, code)) left[code] = []\n        const value = right[code]\n        constructs(\n          // @ts-expect-error Looks like a list.\n          left[code],\n          Array.isArray(value) ? value : value ? [value] : []\n        )\n      }\n    }\n  }\n}\n\n/**\n * Merge `list` into `existing` (both lists of constructs).\n * Mutates `existing`.\n *\n * @param {Array<unknown>} existing\n *   List of constructs to merge into.\n * @param {Array<unknown>} list\n *   List of constructs to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction constructs(existing, list) {\n  let index = -1\n  /** @type {Array<unknown>} */\n  const before = []\n\n  while (++index < list.length) {\n    // @ts-expect-error Looks like an object.\n    ;(list[index].add === 'after' ? existing : before).push(list[index])\n  }\n\n  splice(existing, 0, 0, before)\n}\n\n/**\n * Combine multiple HTML extensions into one.\n *\n * @param {ReadonlyArray<HtmlExtension>} htmlExtensions\n *   List of HTML extensions.\n * @returns {HtmlExtension}\n *   Single combined HTML extension.\n */\nexport function combineHtmlExtensions(htmlExtensions) {\n  /** @type {HtmlExtension} */\n  const handlers = {}\n  let index = -1\n\n  while (++index < htmlExtensions.length) {\n    htmlExtension(handlers, htmlExtensions[index])\n  }\n\n  return handlers\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {HtmlExtension} all\n *   Extension to merge into.\n * @param {HtmlExtension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction htmlExtension(all, extension) {\n  /** @type {keyof HtmlExtension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    const left = maybe || (all[hook] = {})\n    const right = extension[hook]\n    /** @type {keyof Handles} */\n    let type\n\n    if (right) {\n      for (type in right) {\n        // @ts-expect-error assume document vs regular handler are managed correctly.\n        left[type] = right[type]\n      }\n    }\n  }\n}\n", "/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n", "/// <reference lib=\"dom\" />\n\n/* global document */\n\nconst element = document.createElement('i')\n\n/**\n * @param {string} value\n * @returns {string | false}\n */\nexport function decodeNamedCharacterReference(value) {\n  const characterReference = '&' + value + ';'\n  element.innerHTML = characterReference\n  const character = element.textContent\n\n  // Some named character references do not require the closing semicolon\n  // (`&not`, for instance), which leads to situations where parsing the assumed\n  // named reference of `&notit;` will result in the string `¬it;`.\n  // When we encounter a trailing semicolon after parsing, and the character\n  // reference to decode was not a semicolon (`&semi;`), we can assume that the\n  // matching was not complete.\n  if (\n    // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n    // yield `null`.\n    character.charCodeAt(character.length - 1) === 59 /* `;` */ &&\n    value !== 'semi'\n  ) {\n    return false\n  }\n\n  // If the decoded string is equal to the input, the character reference was\n  // not valid.\n  // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n  // yield `null`.\n  return character === characterReference ? false : character\n}\n", "import {codes, values} from 'micromark-util-symbol'\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < codes.ht ||\n    code === codes.vt ||\n    (code > codes.cr && code < codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55_295 && code < 57_344) ||\n    // Noncharacters.\n    (code > 64_975 && code < 65_008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65_535) === 65_535 ||\n    (code & 65_535) === 65_534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1_114_111\n  ) {\n    return values.replacementCharacter\n  }\n\n  return String.fromCodePoint(code)\n}\n", "import {values} from 'micromark-util-symbol'\n\n/**\n * Normalize an identifier (as found in references, definitions).\n *\n * Collapses markdown whitespace, trim, and then lower- and uppercase.\n *\n * Some characters are considered “uppercase”, such as U+03F4 (`ϴ`), but if their\n * lowercase counterpart (U+03B8 (`θ`)) is uppercased will result in a different\n * uppercase character (U+0398 (`Θ`)).\n * So, to get a canonical form, we perform both lower- and uppercase.\n *\n * Using uppercase last makes sure keys will never interact with default\n * prototypal values (such as `constructor`): nothing in the prototype of\n * `Object` is uppercase.\n *\n * @param {string} value\n *   Identifier to normalize.\n * @returns {string}\n *   Normalized identifier.\n */\nexport function normalizeIdentifier(value) {\n  return (\n    value\n      // Collapse markdown whitespace.\n      .replace(/[\\t\\n\\r ]+/g, values.space)\n      // Trim.\n      .replace(/^ | $/g, '')\n      // Some characters are considered “uppercase”, but if their lowercase\n      // counterpart is uppercased will result in a different uppercase\n      // character.\n      // Hence, to get that form, we perform both lower- and uppercase.\n      // Upper case makes sure keys will not interact with default prototypal\n      // methods: no method is uppercase.\n      .toLowerCase()\n      .toUpperCase()\n  )\n}\n", "/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {codes} from 'micromark-util-symbol'\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < codes.space || code === codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEnding(code) {\n  return code !== null && code < codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEndingOrSpace(code) {\n  return code !== null && (code < codes.nul || code === codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownSpace(code) {\n  return (\n    code === codes.horizontalTab ||\n    code === codes.virtualSpace ||\n    code === codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodePunctuation = regexCheck(/\\p{P}|\\p{S}/u)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n *   Expression.\n * @returns {(code: Code) => boolean}\n *   Check.\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && code > -1 && regex.test(String.fromCharCode(code))\n  }\n}\n", "import {asciiAlphanumeric} from 'micromark-util-character'\nimport {encode} from 'micromark-util-encode'\nimport {codes, values} from 'micromark-util-symbol'\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | null | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''))\n\n  if (!protocol) {\n    return value\n  }\n\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === codes.percentSign &&\n      asciiAlphanumeric(value.charCodeAt(index + 1)) &&\n      asciiAlphanumeric(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55_295 && code < 57_344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56_320 && next > 56_319 && next < 57_344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = values.replacementCharacter\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n\n  return result.join('') + value.slice(start)\n}\n", "/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport {markdownSpace} from 'micromark-util-character'\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownSpace(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if (markdownSpace(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n", "/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes, constants} from 'micromark-util-symbol'\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (\n    code === codes.eof ||\n    markdownLineEndingOrSpace(code) ||\n    unicodeWhitespace(code)\n  ) {\n    return constants.characterGroupWhitespace\n  }\n\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation\n  }\n}\n", "/**\n * @import {Event, Resolver, TokenizeContext} from 'micromark-util-types'\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {ReadonlyArray<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nexport function resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = []\n  let index = -1\n\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll\n\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context)\n      called.push(resolve)\n    }\n  }\n\n  return events\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blankLine = {partial: true, tokenize: tokenizeBlankLine}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.linePrefix)(code)\n      : after(code)\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Event,\n *   Point,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {push, splice} from 'micromark-util-chunked'\nimport {classify<PERSON><PERSON>cter} from 'micromark-util-classify-character'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const attention = {\n  name: 'attention',\n  resolveAll: resolveAllAttention,\n  tokenize: tokenizeAttention\n}\n\n/**\n * Take all events and resolve attention to emphasis or strong.\n *\n * @type {Resolver}\n */\n// eslint-disable-next-line complexity\nfunction resolveAllAttention(events, context) {\n  let index = -1\n  /** @type {number} */\n  let open\n  /** @type {Token} */\n  let group\n  /** @type {Token} */\n  let text\n  /** @type {Token} */\n  let openingSequence\n  /** @type {Token} */\n  let closingSequence\n  /** @type {number} */\n  let use\n  /** @type {Array<Event>} */\n  let nextEvents\n  /** @type {number} */\n  let offset\n\n  // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === 'attentionSequence' &&\n      events[index][1]._close\n    ) {\n      open = index\n\n      // Now walk back to find an opener.\n      while (open--) {\n        // Find a token that can open the closer.\n        if (\n          events[open][0] === 'exit' &&\n          events[open][1].type === 'attentionSequence' &&\n          events[open][1]._open &&\n          // If the markers are the same:\n          context.sliceSerialize(events[open][1]).charCodeAt(0) ===\n            context.sliceSerialize(events[index][1]).charCodeAt(0)\n        ) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if (\n            (events[open][1]._close || events[index][1]._open) &&\n            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&\n            !(\n              (events[open][1].end.offset -\n                events[open][1].start.offset +\n                events[index][1].end.offset -\n                events[index][1].start.offset) %\n              3\n            )\n          ) {\n            continue\n          }\n\n          // Number of markers to use from the sequence.\n          use =\n            events[open][1].end.offset - events[open][1].start.offset > 1 &&\n            events[index][1].end.offset - events[index][1].start.offset > 1\n              ? 2\n              : 1\n\n          const start = {...events[open][1].end}\n          const end = {...events[index][1].start}\n          movePoint(start, -use)\n          movePoint(end, use)\n\n          openingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start,\n            end: {...events[open][1].end}\n          }\n          closingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start: {...events[index][1].start},\n            end\n          }\n          text = {\n            type: use > 1 ? types.strongText : types.emphasisText,\n            start: {...events[open][1].end},\n            end: {...events[index][1].start}\n          }\n          group = {\n            type: use > 1 ? types.strong : types.emphasis,\n            start: {...openingSequence.start},\n            end: {...closingSequence.end}\n          }\n\n          events[open][1].end = {...openingSequence.start}\n          events[index][1].start = {...closingSequence.end}\n\n          nextEvents = []\n\n          // If there are more markers in the opening, add them before.\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = push(nextEvents, [\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context]\n            ])\n          }\n\n          // Opening.\n          nextEvents = push(nextEvents, [\n            ['enter', group, context],\n            ['enter', openingSequence, context],\n            ['exit', openingSequence, context],\n            ['enter', text, context]\n          ])\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.insideSpan.null,\n            'expected `insideSpan` to be populated'\n          )\n\n          // Between.\n          nextEvents = push(\n            nextEvents,\n            resolveAll(\n              context.parser.constructs.insideSpan.null,\n              events.slice(open + 1, index),\n              context\n            )\n          )\n\n          // Closing.\n          nextEvents = push(nextEvents, [\n            ['exit', text, context],\n            ['enter', closingSequence, context],\n            ['exit', closingSequence, context],\n            ['exit', group, context]\n          ])\n\n          // If there are more markers in the closing, add them after.\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2\n            nextEvents = push(nextEvents, [\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context]\n            ])\n          } else {\n            offset = 0\n          }\n\n          splice(events, open - 1, index - open + 3, nextEvents)\n\n          index = open + nextEvents.length - offset - 2\n          break\n        }\n      }\n    }\n  }\n\n  // Remove remaining sequences.\n  index = -1\n\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data'\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAttention(effects, ok) {\n  const attentionMarkers = this.parser.constructs.attentionMarkers.null\n  const previous = this.previous\n  const before = classifyCharacter(previous)\n\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Before a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(\n      code === codes.asterisk || code === codes.underscore,\n      'expected asterisk or underscore'\n    )\n    marker = code\n    effects.enter('attentionSequence')\n    return inside(code)\n  }\n\n  /**\n   * In a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    const token = effects.exit('attentionSequence')\n\n    // To do: next major: move this to resolver, just like `markdown-rs`.\n    const after = classifyCharacter(code)\n\n    // Always populated by defaults.\n    assert(attentionMarkers, 'expected `attentionMarkers` to be populated')\n\n    const open =\n      !after ||\n      (after === constants.characterGroupPunctuation && before) ||\n      attentionMarkers.includes(code)\n    const close =\n      !before ||\n      (before === constants.characterGroupPunctuation && after) ||\n      attentionMarkers.includes(previous)\n\n    token._open = Boolean(\n      marker === codes.asterisk ? open : open && (before || !close)\n    )\n    token._close = Boolean(\n      marker === codes.asterisk ? close : close && (after || !open)\n    )\n    return ok(code)\n  }\n}\n\n/**\n * Move a point a bit.\n *\n * Note: `move` only works inside lines! It’s not possible to move past other\n * chunks (replacement characters, tabs, or line endings).\n *\n * @param {Point} point\n *   Point.\n * @param {number} offset\n *   Amount to move.\n * @returns {undefined}\n *   Nothing.\n */\nfunction movePoint(point, offset) {\n  point.column += offset\n  point.offset += offset\n  point._bufferIndex += offset\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  asciiAtext,\n  asciiControl\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const autolink = {name: 'autolink', tokenize: tokenizeAutolink}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.autolink)\n    effects.enter(types.autolinkMarker)\n    effects.consume(code)\n    effects.exit(types.autolinkMarker)\n    effects.enter(types.autolinkProtocol)\n    return open\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    if (code === codes.atSign) {\n      return nok(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      code === codes.plusSign ||\n      code === codes.dash ||\n      code === codes.dot ||\n      asciiAlphanumeric(code)\n    ) {\n      // Count the previous alphabetical from `open` too.\n      size = 1\n      return schemeInsideOrEmailAtext(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === codes.colon) {\n      effects.consume(code)\n      size = 0\n      return urlInside\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      (code === codes.plusSign ||\n        code === codes.dash ||\n        code === codes.dot ||\n        asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkSchemeSizeMax\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    size = 0\n    return emailAtext(code)\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.autolinkProtocol)\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    // ASCII control, space, or `<`.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.lessThan ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === codes.atSign) {\n      effects.consume(code)\n      return emailAtSignOrDot\n    }\n\n    if (asciiAtext(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === codes.dot) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(types.autolinkProtocol).type = types.autolinkEmail\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    return emailValue(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if (\n      (code === codes.dash || asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkDomainSizeMax\n    ) {\n      const next = code === codes.dash ? emailValue : emailLabel\n      effects.consume(code)\n      return next\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blockQuote = {\n  continuation: {tokenize: tokenizeBlockQuoteContinuation},\n  exit,\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.greaterThan) {\n      const state = self.containerState\n\n      assert(state, 'expected `containerState` to be defined in container')\n\n      if (!state.open) {\n        effects.enter(types.blockQuote, {_container: true})\n        state.open = true\n      }\n\n      effects.enter(types.blockQuotePrefix)\n      effects.enter(types.blockQuoteMarker)\n      effects.consume(code)\n      effects.exit(types.blockQuoteMarker)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.blockQuotePrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.blockQuotePrefixWhitespace)\n      effects.exit(types.blockQuotePrefix)\n      return ok\n    }\n\n    effects.exit(types.blockQuotePrefix)\n    return ok(code)\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this\n\n  return contStart\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if (markdownSpace(code)) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      return factorySpace(\n        effects,\n        contBefore,\n        types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : constants.tabSize\n      )(code)\n    }\n\n    return contBefore(code)\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code)\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(types.blockQuote)\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {asciiPunctuation} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.characterEscape)\n    effects.enter(types.escapeMarker)\n    effects.consume(code)\n    effects.exit(types.escapeMarker)\n    return inside\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if (asciiPunctuation(code)) {\n      effects.enter(types.characterEscapeValue)\n      effects.consume(code)\n      effects.exit(types.characterEscapeValue)\n      effects.exit(types.characterEscape)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {\n  asciiAlphanumeric,\n  asciiDigit,\n  asciiHexDigit\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this\n  let size = 0\n  /** @type {number} */\n  let max\n  /** @type {(code: Code) => boolean} */\n  let test\n\n  return start\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.ampersand, 'expected `&`')\n    effects.enter(types.characterReference)\n    effects.enter(types.characterReferenceMarker)\n    effects.consume(code)\n    effects.exit(types.characterReferenceMarker)\n    return open\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.characterReferenceMarkerNumeric)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerNumeric)\n      return numeric\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceNamedSizeMax\n    test = asciiAlphanumeric\n    return value(code)\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter(types.characterReferenceMarkerHexadecimal)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerHexadecimal)\n      effects.enter(types.characterReferenceValue)\n      max = constants.characterReferenceHexadecimalSizeMax\n      test = asciiHexDigit\n      return value\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceDecimalSizeMax\n    test = asciiDigit\n    return value(code)\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === codes.semicolon && size) {\n      const token = effects.exit(types.characterReferenceValue)\n\n      if (\n        test === asciiAlphanumeric &&\n        !decodeNamedCharacterReference(self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(types.characterReferenceMarker)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarker)\n      effects.exit(types.characterReference)\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuation\n}\n\n/** @type {Construct} */\nexport const codeFenced = {\n  concrete: true,\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this\n  /** @type {Construct} */\n  const closeStart = {partial: true, tokenize: tokenizeCloseStart}\n  let initialPrefix = 0\n  let sizeOpen = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code)\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(\n      code === codes.graveAccent || code === codes.tilde,\n      'expected `` ` `` or `~`'\n    )\n\n    const tail = self.events[self.events.length - 1]\n    initialPrefix =\n      tail && tail[1].type === types.linePrefix\n        ? tail[2].sliceSerialize(tail[1], true).length\n        : 0\n\n    marker = code\n    effects.enter(types.codeFenced)\n    effects.enter(types.codeFencedFence)\n    effects.enter(types.codeFencedFenceSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code)\n    }\n\n    effects.exit(types.codeFencedFenceSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, infoBefore, types.whitespace)(code)\n      : infoBefore(code)\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence)\n      return self.interrupt\n        ? ok(code)\n        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFencedFenceInfo)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return info(code)\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return infoBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return factorySpace(effects, metaBefore, types.whitespace)(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return info\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code)\n    }\n\n    effects.enter(types.codeFencedFenceMeta)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceMeta)\n      return infoBefore(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    return effects.attempt(closeStart, after, contentBefore)(code)\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return contentStart\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code)\n      ? factorySpace(\n          effects,\n          beforeContentChunk,\n          types.linePrefix,\n          initialPrefix + 1\n        )(code)\n      : beforeContentChunk(code)\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return contentChunk(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced)\n    return ok(code)\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0\n\n    return startBefore\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol')\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return start\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence)\n      return markdownSpace(code)\n        ? factorySpace(\n            effects,\n            beforeSequenceClose,\n            types.linePrefix,\n            self.parser.constructs.disable.null.includes('codeIndented')\n              ? undefined\n              : constants.tabSize\n          )(code)\n        : beforeSequenceClose(code)\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence)\n        return sequenceClose(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence)\n        return markdownSpace(code)\n          ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code)\n          : sequenceCloseAfter(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineStart\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented\n}\n\n/** @type {Construct} */\nconst furtherStart = {partial: true, tokenize: tokenizeFurtherStart}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  const self = this\n  return start\n\n  /**\n   * Start of code (indented).\n   *\n   * > **Parsing note**: it is not needed to check if this first line is a\n   * > filled line (that it has a non-whitespace character), because blank lines\n   * > are parsed already, so we never run into that.\n   *\n   * ```markdown\n   * > |     aaa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: manually check if interrupting like `markdown-rs`.\n    assert(markdownSpace(code))\n    effects.enter(types.codeIndented)\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? atBreak(code)\n      : nok(code)\n  }\n\n  /**\n   * At a break.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.eof) {\n      return after(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      return effects.attempt(furtherStart, atBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return inside(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return inside\n  }\n\n  /** @type {State} */\n  function after(code) {\n    effects.exit(types.codeIndented)\n    // To do: allow interrupting like `markdown-rs`.\n    // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeFurtherStart(effects, ok, nok) {\n  const self = this\n\n  return furtherStart\n\n  /**\n   * At eol, trying to parse another indent.\n   *\n   * ```markdown\n   * > |     aaa\n   *            ^\n   *   |     bbb\n   * ```\n   *\n   * @type {State}\n   */\n  function furtherStart(code) {\n    // To do: improve `lazy` / `pierce` handling.\n    // If this is a lazy line, it can’t be code.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return furtherStart\n    }\n\n    // To do: the code here in `micromark-js` is a bit different from\n    // `markdown-rs` because there it can attempt spaces.\n    // We can’t yet.\n    //\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? ok(code)\n      : markdownLineEnding(code)\n        ? furtherStart(code)\n        : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Previous,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const codeText = {\n  name: 'codeText',\n  previous,\n  resolve: resolveCodeText,\n  tokenize: tokenizeCodeText\n}\n\n// To do: next major: don’t resolve, like `markdown-rs`.\n/** @type {Resolver} */\nfunction resolveCodeText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === types.codeTextData) {\n        // Then we have padding.\n        events[headEnterIndex][1].type = types.codeTextPadding\n        events[tailExitIndex][1].type = types.codeTextPadding\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === types.lineEnding\n    ) {\n      events[enter][1].type = types.codeTextData\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== codes.graveAccent ||\n    this.events[this.events.length - 1][1].type === types.characterEscape\n  )\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeText(effects, ok, nok) {\n  const self = this\n  let sizeOpen = 0\n  /** @type {number} */\n  let size\n  /** @type {Token} */\n  let token\n\n  return start\n\n  /**\n   * Start of code (text).\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * > | \\`a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.graveAccent, 'expected `` ` ``')\n    assert(previous.call(self, self.previous), 'expected correct previous')\n    effects.enter(types.codeText)\n    effects.enter(types.codeTextSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    effects.exit(types.codeTextSequence)\n    return between(code)\n  }\n\n  /**\n   * Between something and something else.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function between(code) {\n    // EOF.\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // To do: next major: don’t do spaces in resolve, but when compiling,\n    // like `markdown-rs`.\n    // Tabs don’t work, and virtual spaces don’t make sense.\n    if (code === codes.space) {\n      effects.enter('space')\n      effects.consume(code)\n      effects.exit('space')\n      return between\n    }\n\n    // Closing fence? Could also be data.\n    if (code === codes.graveAccent) {\n      token = effects.enter(types.codeTextSequence)\n      size = 0\n      return sequenceClose(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return between\n    }\n\n    // Data.\n    effects.enter(types.codeTextData)\n    return data(code)\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.graveAccent ||\n      markdownLineEnding(code)\n    ) {\n      effects.exit(types.codeTextData)\n      return between(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n\n  /**\n   * In closing sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceClose(code) {\n    // More.\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      size++\n      return sequenceClose\n    }\n\n    // Done!\n    if (size === sizeOpen) {\n      effects.exit(types.codeTextSequence)\n      effects.exit(types.codeText)\n      return ok(code)\n    }\n\n    // More or less accents: mark as data.\n    token.type = types.codeTextData\n    return data(code)\n  }\n}\n", "import {constants} from 'micromark-util-symbol'\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */\nexport class SpliceBuffer {\n  /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */\n  constructor(initial) {\n    /** @type {Array<T>} */\n    this.left = initial ? [...initial] : []\n    /** @type {Array<T>} */\n    this.right = []\n  }\n\n  /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */\n  get(index) {\n    if (index < 0 || index >= this.left.length + this.right.length) {\n      throw new RangeError(\n        'Cannot access index `' +\n          index +\n          '` in a splice buffer of size `' +\n          (this.left.length + this.right.length) +\n          '`'\n      )\n    }\n\n    if (index < this.left.length) return this.left[index]\n    return this.right[this.right.length - index + this.left.length - 1]\n  }\n\n  /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */\n  get length() {\n    return this.left.length + this.right.length\n  }\n\n  /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  shift() {\n    this.setCursor(0)\n    return this.right.pop()\n  }\n\n  /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */\n  slice(start, end) {\n    /** @type {number} */\n    const stop =\n      end === null || end === undefined ? Number.POSITIVE_INFINITY : end\n\n    if (stop < this.left.length) {\n      return this.left.slice(start, stop)\n    }\n\n    if (start > this.left.length) {\n      return this.right\n        .slice(\n          this.right.length - stop + this.left.length,\n          this.right.length - start + this.left.length\n        )\n        .reverse()\n    }\n\n    return this.left\n      .slice(start)\n      .concat(\n        this.right.slice(this.right.length - stop + this.left.length).reverse()\n      )\n  }\n\n  /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */\n  splice(start, deleteCount, items) {\n    /** @type {number} */\n    const count = deleteCount || 0\n\n    this.setCursor(Math.trunc(start))\n    const removed = this.right.splice(\n      this.right.length - count,\n      Number.POSITIVE_INFINITY\n    )\n    if (items) chunkedPush(this.left, items)\n    return removed.reverse()\n  }\n\n  /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  pop() {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    return this.left.pop()\n  }\n\n  /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  push(item) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    this.left.push(item)\n  }\n\n  /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  pushMany(items) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    chunkedPush(this.left, items)\n  }\n\n  /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshift(item) {\n    this.setCursor(0)\n    this.right.push(item)\n  }\n\n  /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshiftMany(items) {\n    this.setCursor(0)\n    chunkedPush(this.right, items.reverse())\n  }\n\n  /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */\n  setCursor(n) {\n    if (\n      n === this.left.length ||\n      (n > this.left.length && this.right.length === 0) ||\n      (n < 0 && this.left.length === 0)\n    )\n      return\n    if (n < this.left.length) {\n      // Move cursor to the this.left\n      const removed = this.left.splice(n, Number.POSITIVE_INFINITY)\n      chunkedPush(this.right, removed.reverse())\n    } else {\n      // Move cursor to the this.right\n      const removed = this.right.splice(\n        this.left.length + this.right.length - n,\n        Number.POSITIVE_INFINITY\n      )\n      chunkedPush(this.left, removed.reverse())\n    }\n  }\n}\n\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */\nfunction chunkedPush(list, right) {\n  /** @type {number} */\n  let chunkStart = 0\n\n  if (right.length < constants.v8MaxSafeChunkSize) {\n    list.push(...right)\n  } else {\n    while (chunkStart < right.length) {\n      list.push(\n        ...right.slice(chunkStart, chunkStart + constants.v8MaxSafeChunkSize)\n      )\n      chunkStart += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n", "/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, types} from 'micromark-util-symbol'\nimport {<PERSON>plice<PERSON>uffer} from './lib/splice-buffer.js'\n\n// Hidden API exposed for testing.\nexport {SpliceBuffer} from './lib/splice-buffer.js'\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\n// eslint-disable-next-line complexity\nexport function subtokenize(eventsArray) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n  const events = new SpliceBuffer(eventsArray)\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events.get(index)\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === types.chunkFlow &&\n      events.get(index - 1)[1].type === types.listItemPrefix\n    ) {\n      assert(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events.get(otherIndex)\n\n        if (\n          otherEvent[1].type === types.lineEnding ||\n          otherEvent[1].type === types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events.get(lineIndex)[1].type = types.lineEndingBlank\n            }\n\n            otherEvent[1].type = types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else if (\n          otherEvent[1].type === types.linePrefix ||\n          otherEvent[1].type === types.listItemIndent\n        ) {\n          // Move past.\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = {...events.get(lineIndex)[1].start}\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        events.splice(lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  // The changes to the `events` buffer must be copied back into the eventsArray\n  splice(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0))\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */\nfunction subcontent(events, eventIndex) {\n  const token = events.get(eventIndex)[1]\n  const context = events.get(eventIndex)[2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  assert(token.contentType, 'expected `contentType` on subtokens')\n\n  let tokenizer = token._tokenizer\n\n  if (!tokenizer) {\n    tokenizer = context.parser[token.contentType](token.start)\n\n    if (token._contentTypeTextTrailing) {\n      tokenizer._contentTypeTextTrailing = true\n    }\n  }\n\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events.get(++startPosition)[1] !== current) {\n      // Empty.\n    }\n\n    assert(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    assert(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      assert(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    assert(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    assert(start !== undefined, 'expected a start position when splicing')\n    jumps.push([start, start + slice.length - 1])\n    events.splice(start, 2, slice)\n  }\n\n  jumps.reverse()\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n", "/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {subtokenize} from 'micromark-util-subtokenize'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nexport const content = {resolve: resolveContent, tokenize: tokenizeContent}\n\n/** @type {Construct} */\nconst continuationConstruct = {partial: true, tokenize: tokenizeContinuation}\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  subtokenize(events)\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous\n\n  return chunkStart\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected no eof or eol'\n    )\n\n    effects.enter(types.content)\n    previous = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent\n    })\n    return chunkInside(code)\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === codes.eof) {\n      return contentEnd(code)\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if (markdownLineEnding(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    }\n\n    // Data.\n    effects.consume(code)\n    return chunkInside\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(types.chunkContent)\n    effects.exit(types.content)\n    return ok(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.consume(code)\n    effects.exit(types.chunkContent)\n    assert(previous, 'expected previous token')\n    previous.next = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent,\n      previous\n    })\n    previous = previous.next\n    return chunkInside\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this\n\n  return startLookahead\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.exit(types.chunkContent)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, prefixed, types.linePrefix)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n\n    const tail = self.events[self.events.length - 1]\n\n    if (\n      !self.parser.constructs.disable.null.includes('codeIndented') &&\n      tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n    ) {\n      return ok(code)\n    }\n\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n  }\n}\n", "/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport {\n  asciiControl,\n  markdownLineEndingOrSpace,\n  markdownLineEnding\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Parse destinations.\n *\n * ###### Examples\n *\n * ```markdown\n * <a>\n * <a\\>b>\n * <a b>\n * <a)>\n * a\n * a\\)b\n * a(b)c\n * a(b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type for whole (`<a>` or `b`).\n * @param {TokenType} literalType\n *   Type when enclosed (`<a>`).\n * @param {TokenType} literalMarkerType\n *   Type for enclosing (`<` and `>`).\n * @param {TokenType} rawType\n *   Type when not enclosed (`b`).\n * @param {TokenType} stringType\n *   Type for the value (`a` or `b`).\n * @param {number | undefined} [max=Infinity]\n *   Depth of nested parens (inclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factoryDestination(\n  effects,\n  ok,\n  nok,\n  type,\n  literalType,\n  literalMarkerType,\n  rawType,\n  stringType,\n  max\n) {\n  const limit = max || Number.POSITIVE_INFINITY\n  let balance = 0\n\n  return start\n\n  /**\n   * Start of destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *     ^\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.lessThan) {\n      effects.enter(type)\n      effects.enter(literalType)\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      return enclosedBefore\n    }\n\n    // ASCII control, space, closing paren.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.rightParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter(type)\n    effects.enter(rawType)\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return raw(code)\n  }\n\n  /**\n   * After `<`, at an enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedBefore(code) {\n    if (code === codes.greaterThan) {\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      effects.exit(literalType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return enclosed(code)\n  }\n\n  /**\n   * In enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosed(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      return enclosedBefore(code)\n    }\n\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      markdownLineEnding(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? enclosedEscape : enclosed\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | <a\\*a>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedEscape(code) {\n    if (\n      code === codes.lessThan ||\n      code === codes.greaterThan ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return enclosed\n    }\n\n    return enclosed(code)\n  }\n\n  /**\n   * In raw destination.\n   *\n   * ```markdown\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function raw(code) {\n    if (\n      !balance &&\n      (code === codes.eof ||\n        code === codes.rightParenthesis ||\n        markdownLineEndingOrSpace(code))\n    ) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      effects.exit(rawType)\n      effects.exit(type)\n      return ok(code)\n    }\n\n    if (balance < limit && code === codes.leftParenthesis) {\n      effects.consume(code)\n      balance++\n      return raw\n    }\n\n    if (code === codes.rightParenthesis) {\n      effects.consume(code)\n      balance--\n      return raw\n    }\n\n    // ASCII control (but *not* `\\0`) and space and `(`.\n    // Note: in `markdown-rs`, `\\0` exists in codes, in `micromark-js` it\n    // doesn’t.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.leftParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? rawEscape : raw\n  }\n\n  /**\n   * After `\\`, at special character.\n   *\n   * ```markdown\n   * > | a\\*a\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function rawEscape(code) {\n    if (\n      code === codes.leftParenthesis ||\n      code === codes.rightParenthesis ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return raw\n    }\n\n    return raw(code)\n  }\n}\n", "/**\n * @import {\n *   Effects,\n *   State,\n *   TokenizeContext,\n *   TokenType\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Parse labels.\n *\n * > 👉 **Note**: labels in markdown are capped at 999 characters in the string.\n *\n * ###### Examples\n *\n * ```markdown\n * [a]\n * [a\n * b]\n * [a\\]b]\n * ```\n *\n * @this {TokenizeContext}\n *   Tokenize context.\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole label (`[a]`).\n * @param {TokenType} markerType\n *   Type for the markers (`[` and `]`).\n * @param {TokenType} stringType\n *   Type for the identifier (`a`).\n * @returns {State}\n *   Start state.\n */\nexport function factoryLabel(effects, ok, nok, type, markerType, stringType) {\n  const self = this\n  let size = 0\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /**\n   * Start of label.\n   *\n   * ```markdown\n   * > | [a]\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(type)\n    effects.enter(markerType)\n    effects.consume(code)\n    effects.exit(markerType)\n    effects.enter(stringType)\n    return atBreak\n  }\n\n  /**\n   * In label, at something, before something else.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (\n      size > constants.linkReferenceSizeMax ||\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      (code === codes.rightSquareBracket && !seen) ||\n      // To do: remove in the future once we’ve switched from\n      // `micromark-extension-footnote` to `micromark-extension-gfm-footnote`,\n      // which doesn’t need this.\n      // Hidden footnotes hook.\n      /* c8 ignore next 3 */\n      (code === codes.caret &&\n        !size &&\n        '_hiddenFootnoteSupport' in self.parser.constructs)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit(stringType)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    // To do: indent? Link chunks and EOLs together?\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return atBreak\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return labelInside(code)\n  }\n\n  /**\n   * In label, in text.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      code === codes.rightSquareBracket ||\n      markdownLineEnding(code) ||\n      size++ > constants.linkReferenceSizeMax\n    ) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    if (!seen) seen = !markdownSpace(code)\n    return code === codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | [a\\*a]\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Effects,\n *   State,\n *   TokenType\n * } from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Parse titles.\n *\n * ###### Examples\n *\n * ```markdown\n * \"a\"\n * 'b'\n * (c)\n * \"a\n * b\"\n * 'a\n *     b'\n * (a\\)b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole title (`\"a\"`, `'b'`, `(c)`).\n * @param {TokenType} markerType\n *   Type for the markers (`\"`, `'`, `(`, and `)`).\n * @param {TokenType} stringType\n *   Type for the value (`a`).\n * @returns {State}\n *   Start state.\n */\nexport function factoryTitle(effects, ok, nok, type, markerType, stringType) {\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of title.\n   *\n   * ```markdown\n   * > | \"a\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      effects.enter(type)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      marker = code === codes.leftParenthesis ? codes.rightParenthesis : code\n      return begin\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After opening marker.\n   *\n   * This is also used at the closing marker.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function begin(code) {\n    if (code === marker) {\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    return atBreak(code)\n  }\n\n  /**\n   * At something, before something else.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.exit(stringType)\n      return begin(marker)\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // Note: blank lines can’t exist in content.\n    if (markdownLineEnding(code)) {\n      // To do: use `space_or_tab_eol_with_options`, connect.\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return factorySpace(effects, atBreak, types.linePrefix)\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return inside(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker || code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? escape : inside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | \"a\\*b\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function escape(code) {\n    if (code === marker || code === codes.backslash) {\n      effects.consume(code)\n      return inside\n    }\n\n    return inside(code)\n  }\n}\n", "/**\n * @import {Effects, State} from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol'\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   line endings or spaces in markdown are often optional, in which case this\n *     factory can be used and `ok` will be switched to whether spaces were found\n *     or not\n * *   one line ending or space can be detected with\n *     `markdownLineEndingOrSpace(code)` right before using `factoryWhitespace`\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @returns {State}\n *   Start state.\n */\nexport function factoryWhitespace(effects, ok) {\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      seen = true\n      return start\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        start,\n        seen ? types.linePrefix : types.lineSuffix\n      )(code)\n    }\n\n    return ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factorySpace} from 'micromark-factory-space'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const definition = {name: 'definition', tokenize: tokenizeDefinition}\n\n/** @type {Construct} */\nconst titleBefore = {partial: true, tokenize: tokenizeTitleBefore}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this\n  /** @type {string} */\n  let identifier\n\n  return start\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(types.definition)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    return factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionLabel,\n      types.definitionLabelMarker,\n      types.definitionLabelString\n    )(code)\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === codes.colon) {\n      effects.enter(types.definitionMarker)\n      effects.consume(code)\n      effects.exit(types.definitionMarker)\n      return markerAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, destinationBefore)(code)\n      : destinationBefore(code)\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return factoryDestination(\n      effects,\n      destinationAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionDestination,\n      types.definitionDestinationLiteral,\n      types.definitionDestinationLiteralMarker,\n      types.definitionDestinationRaw,\n      types.definitionDestinationString\n    )(code)\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code)\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, afterWhitespace, types.whitespace)(code)\n      : afterWhitespace(code)\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.definition)\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier)\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, beforeMarker)(code)\n      : nok(code)\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return factoryTitle(\n      effects,\n      titleAfter,\n      nok,\n      types.definitionTitle,\n      types.definitionTitleMarker,\n      types.definitionTitleString\n    )(code)\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          titleAfterOptionalWhitespace,\n          types.whitespace\n        )(code)\n      : titleAfterOptionalWhitespace(code)\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.hardBreakEscape)\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit(types.hardBreakEscape)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const headingAtx = {\n  name: 'headingAtx',\n  resolve: resolveHeadingAtx,\n  tokenize: tokenizeHeadingAtx\n}\n\n/** @type {Resolver} */\nfunction resolveHeadingAtx(events, context) {\n  let contentEnd = events.length - 2\n  let contentStart = 3\n  /** @type {Token} */\n  let content\n  /** @type {Token} */\n  let text\n\n  // Prefix whitespace, part of the opening.\n  if (events[contentStart][1].type === types.whitespace) {\n    contentStart += 2\n  }\n\n  // Suffix whitespace, part of the closing.\n  if (\n    contentEnd - 2 > contentStart &&\n    events[contentEnd][1].type === types.whitespace\n  ) {\n    contentEnd -= 2\n  }\n\n  if (\n    events[contentEnd][1].type === types.atxHeadingSequence &&\n    (contentStart === contentEnd - 1 ||\n      (contentEnd - 4 > contentStart &&\n        events[contentEnd - 2][1].type === types.whitespace))\n  ) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4\n  }\n\n  if (contentEnd > contentStart) {\n    content = {\n      type: types.atxHeadingText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    }\n    text = {\n      type: types.chunkText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: constants.contentTypeText\n    }\n\n    splice(events, contentStart, contentEnd - contentStart + 1, [\n      ['enter', content, context],\n      ['enter', text, context],\n      ['exit', text, context],\n      ['exit', content, context]\n    ])\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of a heading (atx).\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    effects.enter(types.atxHeading)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `#`.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.numberSign, 'expected `#`')\n    effects.enter(types.atxHeadingSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (\n      code === codes.numberSign &&\n      size++ < constants.atxHeadingOpeningFenceSizeMax\n    ) {\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    // Always at least one `#`.\n    if (code === codes.eof || markdownLineEndingOrSpace(code)) {\n      effects.exit(types.atxHeadingSequence)\n      return atBreak(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ## aa\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.atxHeadingSequence)\n      return sequenceFurther(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.atxHeading)\n      // To do: interrupt like `markdown-rs`.\n      // // Feel free to interrupt.\n      // tokenizer.interrupt = false\n      return ok(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, atBreak, types.whitespace)(code)\n    }\n\n    // To do: generate `data` tokens, add the `text` token later.\n    // Needs edit map, see: `markdown.rs`.\n    effects.enter(types.atxHeadingText)\n    return data(code)\n  }\n\n  /**\n   * In further sequence (after whitespace).\n   *\n   * Could be normal “visible” hashes in the heading or a final sequence.\n   *\n   * ```markdown\n   * > | ## aa ##\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceFurther(code) {\n    if (code === codes.numberSign) {\n      effects.consume(code)\n      return sequenceFurther\n    }\n\n    effects.exit(types.atxHeadingSequence)\n    return atBreak(code)\n  }\n\n  /**\n   * In text.\n   *\n   * ```markdown\n   * > | ## aa\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.numberSign ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.atxHeadingText)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n}\n", "/**\n * List of lowercase HTML “block” tag names.\n *\n * The list, when parsing HTML (flow), results in more relaxed rules (condition\n * 6).\n * Because they are known blocks, the HTML-like syntax doesn’t have to be\n * strictly parsed.\n * For tag names not in this list, a more strict algorithm (condition 7) is used\n * to detect whether the HTML-like syntax is seen as HTML (flow) or not.\n *\n * This is copied from:\n * <https://spec.commonmark.org/0.30/#html-blocks>.\n *\n * > 👉 **Note**: `search` was added in `CommonMark@0.31`.\n */\nexport const htmlBlockNames = [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'search',\n  'section',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n]\n\n/**\n * List of lowercase HTML “raw” tag names.\n *\n * The list, when parsing HTML (flow), results in HTML that can include lines\n * without exiting, until a closing tag also in this list is found (condition\n * 1).\n *\n * This module is copied from:\n * <https://spec.commonmark.org/0.30/#html-blocks>.\n *\n * > 👉 **Note**: `textarea` was added in `CommonMark@0.30`.\n */\nexport const htmlRawNames = ['pre', 'script', 'style', 'textarea']\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {htmlBlockNames, htmlRawNames} from 'micromark-util-html-tag-name'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {blankLine} from './blank-line.js'\n\n/** @type {Construct} */\nexport const htmlFlow = {\n  concrete: true,\n  name: 'htmlFlow',\n  resolveTo: resolveToHtmlFlow,\n  tokenize: tokenizeHtmlFlow\n}\n\n/** @type {Construct} */\nconst blankLineBefore = {partial: true, tokenize: tokenizeBlankLineBefore}\nconst nonLazyContinuationStart = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuationStart\n}\n\n/** @type {Resolver} */\nfunction resolveToHtmlFlow(events) {\n  let index = events.length\n\n  while (index--) {\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === types.htmlFlow\n    ) {\n      break\n    }\n  }\n\n  if (index > 1 && events[index - 2][1].type === types.linePrefix) {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start\n    // Add the prefix start to the HTML line token.\n    events[index + 1][1].start = events[index - 2][1].start\n    // Remove the line prefix.\n    events.splice(index - 2, 2)\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  const self = this\n  /** @type {number} */\n  let marker\n  /** @type {boolean} */\n  let closingTag\n  /** @type {string} */\n  let buffer\n  /** @type {number} */\n  let index\n  /** @type {Code} */\n  let markerB\n\n  return start\n\n  /**\n   * Start of HTML (flow).\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * At `<`, after optional whitespace.\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlFlow)\n    effects.enter(types.htmlFlowData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | <x />\n   *      ^\n   * > | <!doctype>\n   *      ^\n   * > | <!--xxx-->\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      closingTag = true\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      marker = constants.htmlInstruction\n      // To do:\n      // tokenizer.concrete = true\n      // To do: use `markdown-rs` style interrupt.\n      // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *       ^\n   * > | <!--xxx-->\n   *       ^\n   * > | <![CDATA[>&<]]>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      marker = constants.htmlComment\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      marker = constants.htmlCdata\n      index = 0\n      return cdataOpenInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      marker = constants.htmlDeclaration\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!-`, inside a comment, at another `-`.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<![`, inside CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *        ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n\n      if (index === value.length) {\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok : continuation\n      }\n\n      return cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | </x>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In tag name.\n   *\n   * ```markdown\n   * > | <ab>\n   *      ^^\n   * > | </ab>\n   *       ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagName(code) {\n    if (\n      code === codes.eof ||\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      const slash = code === codes.slash\n      const name = buffer.toLowerCase()\n\n      if (!slash && !closingTag && htmlRawNames.includes(name)) {\n        marker = constants.htmlRaw\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      if (htmlBlockNames.includes(buffer.toLowerCase())) {\n        marker = constants.htmlBasic\n\n        if (slash) {\n          effects.consume(code)\n          return basicSelfClosing\n        }\n\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      marker = constants.htmlComplete\n      // Do not support complete HTML when interrupting.\n      return self.interrupt && !self.parser.lazy[self.now().line]\n        ? nok(code)\n        : closingTag\n          ? completeClosingTagAfter(code)\n          : completeAttributeNameBefore(code)\n    }\n\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a basic tag name.\n   *\n   * ```markdown\n   * > | <div/>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function basicSelfClosing(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuation\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a complete tag name.\n   *\n   * ```markdown\n   * > | <x/>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeClosingTagAfter(code) {\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeClosingTagAfter\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * At an attribute name.\n   *\n   * At first, this state is used after a complete tag name, after whitespace,\n   * where it expects optional attributes or the end of the tag.\n   * It is also reused after attributes, when expecting more optional\n   * attributes.\n   *\n   * ```markdown\n   * > | <a />\n   *        ^\n   * > | <a :b>\n   *        ^\n   * > | <a _b>\n   *        ^\n   * > | <a b>\n   *        ^\n   * > | <a >\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameBefore(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return completeEnd\n    }\n\n    // ASCII alphanumerical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameBefore\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | <a :b>\n   *         ^\n   * > | <a _b>\n   *         ^\n   * > | <a b>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeName(code) {\n    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    return completeAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, at an optional initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b>\n   *         ^\n   * > | <a b=c>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameAfter\n    }\n\n    return completeAttributeNameBefore(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * > | <a b=\"c\">\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      markerB = code\n      return completeAttributeValueQuoted\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    return completeAttributeValueUnquoted(code)\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *           ^\n   * > | <a b='c'>\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuoted(code) {\n    if (code === markerB) {\n      effects.consume(code)\n      markerB = null\n      return completeAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.slash ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return completeAttributeNameAfter(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the\n   * end of the tag.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownSpace(code)\n    ) {\n      return completeAttributeNameBefore(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a complete tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeEnd(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>` in a complete tag.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAfter(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return continuation(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In continuation of any HTML kind.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuation(code) {\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationCommentInside\n    }\n\n    if (code === codes.lessThan && marker === constants.htmlRaw) {\n      effects.consume(code)\n      return continuationRawTagOpen\n    }\n\n    if (code === codes.greaterThan && marker === constants.htmlDeclaration) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (code === codes.questionMark && marker === constants.htmlInstruction) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    if (code === codes.rightSquareBracket && marker === constants.htmlCdata) {\n      effects.consume(code)\n      return continuationCdataInside\n    }\n\n    if (\n      markdownLineEnding(code) &&\n      (marker === constants.htmlBasic || marker === constants.htmlComplete)\n    ) {\n      effects.exit(types.htmlFlowData)\n      return effects.check(\n        blankLineBefore,\n        continuationAfter,\n        continuationStart\n      )(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationStart(code)\n    }\n\n    effects.consume(code)\n    return continuation\n  }\n\n  /**\n   * In continuation, at eol.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStart(code) {\n    return effects.check(\n      nonLazyContinuationStart,\n      continuationStartNonLazy,\n      continuationAfter\n    )(code)\n  }\n\n  /**\n   * In continuation, at eol, before non-lazy content.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStartNonLazy(code) {\n    assert(markdownLineEnding(code))\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return continuationBefore\n  }\n\n  /**\n   * In continuation, before non-lazy content.\n   *\n   * ```markdown\n   *   | <x>\n   * > | asd\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return continuationStart(code)\n    }\n\n    effects.enter(types.htmlFlowData)\n    return continuation(code)\n  }\n\n  /**\n   * In comment continuation, after one `-`, expecting another.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCommentInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `<`, at `/`.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawTagOpen(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      buffer = ''\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `</`, in a raw tag name.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                             ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawEndTag(code) {\n    if (code === codes.greaterThan) {\n      const name = buffer.toLowerCase()\n\n      if (htmlRawNames.includes(name)) {\n        effects.consume(code)\n        return continuationClose\n      }\n\n      return continuation(code)\n    }\n\n    if (asciiAlpha(code) && buffer.length < constants.htmlRawSizeMax) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In cdata continuation, after `]`, expecting `]>`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *                  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCdataInside(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In declaration or instruction continuation, at `>`.\n   *\n   * ```markdown\n   * > | <!-->\n   *         ^\n   * > | <?>\n   *       ^\n   * > | <!q>\n   *        ^\n   * > | <!--ab-->\n   *             ^\n   * > | <![CDATA[>&<]]>\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationDeclarationInside(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    // More dashes.\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In closed continuation: everything we get until the eol/eof is part of it.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationClose(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationAfter(code)\n    }\n\n    effects.consume(code)\n    return continuationClose\n  }\n\n  /**\n   * Done.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationAfter(code) {\n    effects.exit(types.htmlFlow)\n    // // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    // // No longer concrete.\n    // tokenizer.concrete = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuationStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * At eol, before continuation.\n   *\n   * ```markdown\n   * > | * ```js\n   *            ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * A continuation.\n   *\n   * ```markdown\n   *   | * ```js\n   * > | b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLineBefore(effects, ok, nok) {\n  return start\n\n  /**\n   * Before eol, expecting blank line.\n   *\n   * ```markdown\n   * > | <div>\n   *          ^\n   *   |\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return effects.attempt(blankLine, ok, nok)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const htmlText = {name: 'htmlText', tokenize: tokenizeHtmlText}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlText(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code> | undefined} */\n  let marker\n  /** @type {number} */\n  let index\n  /** @type {State} */\n  let returnState\n\n  return start\n\n  /**\n   * Start of HTML (text).\n   *\n   * ```markdown\n   * > | a <b> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlText)\n    effects.enter(types.htmlTextData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | a <b> c\n   *        ^\n   * > | a <!doctype> c\n   *        ^\n   * > | a <!--b--> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instruction\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | a <!doctype> c\n   *         ^\n   * > | a <!--b--> c\n   *         ^\n   * > | a <![CDATA[>&<]]> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      index = 0\n      return cdataOpenInside\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return declaration\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In a comment, after `<!-`, at another `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In comment.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function comment(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = comment\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return comment\n  }\n\n  /**\n   * In comment, after `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentClose(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return comment(code)\n  }\n\n  /**\n   * In comment, after `--`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentEnd(code) {\n    return code === codes.greaterThan\n      ? end(code)\n      : code === codes.dash\n        ? commentClose(code)\n        : comment(code)\n  }\n\n  /**\n   * After `<![`, in CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *          ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === value.length ? cdata : cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In CDATA.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdata(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = cdata\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return cdata\n  }\n\n  /**\n   * In CDATA, after `]`, at another `]`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataClose(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In CDATA, after `]]`, at `>`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataEnd(code) {\n    if (code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In declaration.\n   *\n   * ```markdown\n   * > | a <!b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declaration(code) {\n    if (code === codes.eof || code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = declaration\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return declaration\n  }\n\n  /**\n   * In instruction.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instruction(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instructionClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = instruction\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return instruction\n  }\n\n  /**\n   * In instruction, after `?`, at `>`.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instructionClose(code) {\n    return code === codes.greaterThan ? end(code) : instruction(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</x`, in a tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagClose(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return tagCloseBetween(code)\n  }\n\n  /**\n   * In closing tag, after tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseBetween(code) {\n    if (markdownLineEnding(code)) {\n      returnState = tagCloseBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagCloseBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * After `<x`, in opening tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpen(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In opening tag, after tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenBetween(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return end\n    }\n\n    // ASCII alphabetical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeName(code) {\n    // ASCII alphabetical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    return tagOpenAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, before initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeNameAfter\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeNameAfter\n    }\n\n    return tagOpenBetween(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      marker = code\n      return tagOpenAttributeValueQuoted\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueBefore\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      marker = undefined\n      return tagOpenAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueQuoted\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the end\n   * of the tag.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function end(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      effects.exit(types.htmlTextData)\n      effects.exit(types.htmlText)\n      return ok\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At eol.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   * > | a <!--a\n   *            ^\n   *   | b-->\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingBefore(code) {\n    assert(returnState, 'expected return state')\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.exit(types.htmlTextData)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineEndingAfter\n  }\n\n  /**\n   * After eol, at optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfter(code) {\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          lineEndingAfterPrefix,\n          types.linePrefix,\n          self.parser.constructs.disable.null.includes('codeIndented')\n            ? undefined\n            : constants.tabSize\n        )(code)\n      : lineEndingAfterPrefix(code)\n  }\n\n  /**\n   * After eol, after optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfterPrefix(code) {\n    effects.enter(types.htmlTextData)\n    return returnState(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Event,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {markdownLineEndingOrSpace} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  resolveAll: resolveAllLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  tokenize: tokenizeLabelEnd\n}\n\n/** @type {Construct} */\nconst resourceConstruct = {tokenize: tokenizeResource}\n/** @type {Construct} */\nconst referenceFullConstruct = {tokenize: tokenizeReferenceFull}\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1\n  /** @type {Array<Event>} */\n  const newEvents = []\n  while (++index < events.length) {\n    const token = events[index][1]\n    newEvents.push(events[index])\n\n    if (\n      token.type === types.labelImage ||\n      token.type === types.labelLink ||\n      token.type === types.labelEnd\n    ) {\n      // Remove the marker.\n      const offset = token.type === types.labelImage ? 4 : 2\n      token.type = types.data\n      index += offset\n    }\n  }\n\n  // If the events are equal, we don't have to copy newEvents to events\n  if (events.length !== newEvents.length) {\n    splice(events, 0, events.length, newEvents)\n  }\n\n  return events\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length\n  let offset = 0\n  /** @type {Token} */\n  let token\n  /** @type {number | undefined} */\n  let open\n  /** @type {number | undefined} */\n  let close\n  /** @type {Array<Event>} */\n  let media\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === types.link ||\n        (token.type === types.labelLink && token._inactive)\n      ) {\n        break\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === types.labelImage || token.type === types.labelLink) &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== types.labelLink) {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index\n    }\n  }\n\n  assert(open !== undefined, '`open` is supposed to be found')\n  assert(close !== undefined, '`close` is supposed to be found')\n\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: {...events[open][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  const label = {\n    type: types.label,\n    start: {...events[open][1].start},\n    end: {...events[close][1].end}\n  }\n\n  const text = {\n    type: types.labelText,\n    start: {...events[open + offset + 2][1].end},\n    end: {...events[close - 2][1].start}\n  }\n\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ]\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3))\n\n  // Text open.\n  media = push(media, [['enter', text, context]])\n\n  // Always populated by defaults.\n  assert(\n    context.parser.constructs.insideSpan.null,\n    'expected `insideSpan.null` to be populated'\n  )\n  // Between.\n  media = push(\n    media,\n    resolveAll(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  )\n\n  // Text close, marker close, label close.\n  media = push(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ])\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1))\n\n  // Media close.\n  media = push(media, [['exit', group, context]])\n\n  splice(events, open, events.length, media)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  /** @type {Token} */\n  let labelStart\n  /** @type {boolean} */\n  let defined\n\n  // Find an opening.\n  while (index--) {\n    if (\n      (self.events[index][1].type === types.labelImage ||\n        self.events[index][1].type === types.labelLink) &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`')\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code)\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code)\n    }\n\n    defined = self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize({start: labelStart.end, end: self.now()})\n      )\n    )\n    effects.enter(types.labelEnd)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelEnd)\n    return after\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(\n        resourceConstruct,\n        labelEndOk,\n        defined ? labelEndOk : labelEndNok\n      )(code)\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(\n        referenceFullConstruct,\n        labelEndOk,\n        defined ? referenceNotFull : labelEndNok\n      )(code)\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code)\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(\n      referenceCollapsedConstruct,\n      labelEndOk,\n      labelEndNok\n    )(code)\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code)\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren')\n    effects.enter(types.resource)\n    effects.enter(types.resourceMarker)\n    effects.consume(code)\n    effects.exit(types.resourceMarker)\n    return resourceBefore\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceOpen)(code)\n      : resourceOpen(code)\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code)\n    }\n\n    return factoryDestination(\n      effects,\n      resourceDestinationAfter,\n      resourceDestinationMissing,\n      types.resourceDestination,\n      types.resourceDestinationLiteral,\n      types.resourceDestinationLiteralMarker,\n      types.resourceDestinationRaw,\n      types.resourceDestinationString,\n      constants.linkResourceDestinationBalanceMax\n    )(code)\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceBetween)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code)\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      return factoryTitle(\n        effects,\n        resourceTitleAfter,\n        nok,\n        types.resourceTitle,\n        types.resourceTitleMarker,\n        types.resourceTitleString\n      )(code)\n    }\n\n    return resourceEnd(code)\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceEnd)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker)\n      effects.consume(code)\n      effects.exit(types.resourceMarker)\n      effects.exit(types.resource)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this\n\n  return referenceFull\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    return factoryLabel.call(\n      self,\n      effects,\n      referenceFullAfter,\n      referenceFullMissing,\n      types.reference,\n      types.referenceMarker,\n      types.referenceString\n    )(code)\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    )\n      ? ok(code)\n      : nok(code)\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    effects.enter(types.reference)\n    effects.enter(types.referenceMarker)\n    effects.consume(code)\n    effects.exit(types.referenceMarker)\n    return referenceCollapsedOpen\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker)\n      effects.consume(code)\n      effects.exit(types.referenceMarker)\n      effects.exit(types.reference)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, types} from 'micromark-util-symbol'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartImage = {\n  name: 'labelStartImage',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartImage\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (image) start.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.exclamationMark, 'expected `!`')\n    effects.enter(types.labelImage)\n    effects.enter(types.labelImageMarker)\n    effects.consume(code)\n    effects.exit(types.labelImageMarker)\n    return open\n  }\n\n  /**\n   * After `!`, at `[`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.leftSquareBracket) {\n      effects.enter(types.labelMarker)\n      effects.consume(code)\n      effects.exit(types.labelMarker)\n      effects.exit(types.labelImage)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `![`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *         ^\n   * ```\n   *\n   * This is needed in because, when GFM footnotes are enabled, images never\n   * form when started with a `^`.\n   * Instead, links form:\n   *\n   * ```markdown\n   * ![^a](b)\n   *\n   * ![^a][b]\n   *\n   * [b]: c\n   * ```\n   *\n   * ```html\n   * <p>!<a href=\\\"b\\\">^a</a></p>\n   * <p>!<a href=\\\"c\\\">^a</a></p>\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // To do: use a new field to do this, this is still needed for\n    // `micromark-extension-gfm-footnote`, but the `label-start-link`\n    // behavior isn’t.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, types} from 'micromark-util-symbol'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartLink = {\n  name: 'labelStartLink',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartLink\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(types.labelLink)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelLink)\n    return after\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, ok, types.linePrefix)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(types.thematicBreak)\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(\n      code === codes.asterisk ||\n        code === codes.dash ||\n        code === codes.underscore,\n      'expected `*`, `-`, or `_`'\n    )\n    marker = code\n    return atBreak(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(types.thematicBreakSequence)\n      return sequence(code)\n    }\n\n    if (\n      size >= constants.thematicBreakMarkerCountMin &&\n      (code === codes.eof || markdownLineEnding(code))\n    ) {\n      effects.exit(types.thematicBreak)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit(types.thematicBreakSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, atBreak, types.whitespace)(code)\n      : atBreak(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {asciiDigit, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {blankLine} from './blank-line.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/** @type {Construct} */\nexport const list = {\n  continuation: {tokenize: tokenizeListContinuation},\n  exit: tokenizeListEnd,\n  name: 'list',\n  tokenize: tokenizeListStart\n}\n\n/** @type {Construct} */\nconst listItemPrefixWhitespaceConstruct = {\n  partial: true,\n  tokenize: tokenizeListItemPrefixWhitespace\n}\n\n/** @type {Construct} */\nconst indentConstruct = {partial: true, tokenize: tokenizeIndent}\n\n// To do: `markdown-rs` parses list items on their own and later stitches them\n// together.\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListStart(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  let initialSize =\n    tail && tail[1].type === types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(self.containerState, 'expected state')\n    const kind =\n      self.containerState.type ||\n      (code === codes.asterisk || code === codes.plusSign || code === codes.dash\n        ? types.listUnordered\n        : types.listOrdered)\n\n    if (\n      kind === types.listUnordered\n        ? !self.containerState.marker || code === self.containerState.marker\n        : asciiDigit(code)\n    ) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind\n        effects.enter(kind, {_container: true})\n      }\n\n      if (kind === types.listUnordered) {\n        effects.enter(types.listItemPrefix)\n        return code === codes.asterisk || code === codes.dash\n          ? effects.check(thematicBreak, nok, atMarker)(code)\n          : atMarker(code)\n      }\n\n      if (!self.interrupt || code === codes.digit1) {\n        effects.enter(types.listItemPrefix)\n        effects.enter(types.listItemValue)\n        return inside(code)\n      }\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function inside(code) {\n    assert(self.containerState, 'expected state')\n    if (asciiDigit(code) && ++size < constants.listItemValueSizeMax) {\n      effects.consume(code)\n      return inside\n    }\n\n    if (\n      (!self.interrupt || size < 2) &&\n      (self.containerState.marker\n        ? code === self.containerState.marker\n        : code === codes.rightParenthesis || code === codes.dot)\n    ) {\n      effects.exit(types.listItemValue)\n      return atMarker(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   **/\n  function atMarker(code) {\n    assert(self.containerState, 'expected state')\n    assert(code !== codes.eof, 'eof (`null`) is not a marker')\n    effects.enter(types.listItemMarker)\n    effects.consume(code)\n    effects.exit(types.listItemMarker)\n    self.containerState.marker = self.containerState.marker || code\n    return effects.check(\n      blankLine,\n      // Can’t be empty when interrupting.\n      self.interrupt ? nok : onBlank,\n      effects.attempt(\n        listItemPrefixWhitespaceConstruct,\n        endOfPrefix,\n        otherPrefix\n      )\n    )\n  }\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.initialBlankLine = true\n    initialSize++\n    return endOfPrefix(code)\n  }\n\n  /** @type {State} */\n  function otherPrefix(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.listItemPrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.listItemPrefixWhitespace)\n      return endOfPrefix\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function endOfPrefix(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.size =\n      initialSize +\n      self.sliceSerialize(effects.exit(types.listItemPrefix), true).length\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListContinuation(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  self.containerState._closeFlow = undefined\n\n  return effects.check(blankLine, onBlank, notBlank)\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    assert(typeof self.containerState.size === 'number', 'expected size')\n    self.containerState.furtherBlankLines =\n      self.containerState.furtherBlankLines ||\n      self.containerState.initialBlankLine\n\n    // We have a blank line.\n    // Still, try to consume at most the items size.\n    return factorySpace(\n      effects,\n      ok,\n      types.listItemIndent,\n      self.containerState.size + 1\n    )(code)\n  }\n\n  /** @type {State} */\n  function notBlank(code) {\n    assert(self.containerState, 'expected state')\n    if (self.containerState.furtherBlankLines || !markdownSpace(code)) {\n      self.containerState.furtherBlankLines = undefined\n      self.containerState.initialBlankLine = undefined\n      return notInCurrentItem(code)\n    }\n\n    self.containerState.furtherBlankLines = undefined\n    self.containerState.initialBlankLine = undefined\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)\n  }\n\n  /** @type {State} */\n  function notInCurrentItem(code) {\n    assert(self.containerState, 'expected state')\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true\n    // As we’re closing flow, we’re no longer interrupting.\n    self.interrupt = undefined\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return factorySpace(\n      effects,\n      effects.attempt(list, ok, nok),\n      types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : constants.tabSize\n    )(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  assert(typeof self.containerState.size === 'number', 'expected size')\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemIndent,\n    self.containerState.size + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    assert(self.containerState, 'expected state')\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.listItemIndent &&\n      tail[2].sliceSerialize(tail[1], true).length === self.containerState.size\n      ? ok(code)\n      : nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Exiter}\n */\nfunction tokenizeListEnd(effects) {\n  assert(this.containerState, 'expected state')\n  assert(typeof this.containerState.type === 'string', 'expected type')\n  effects.exit(this.containerState.type)\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  const self = this\n\n  // Always populated by defaults.\n  assert(\n    self.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemPrefixWhitespace,\n    self.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n\n    return !markdownSpace(code) &&\n      tail &&\n      tail[1].type === types.listItemPrefixWhitespace\n      ? ok(code)\n      : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const setextUnderline = {\n  name: 'setextUnderline',\n  resolveTo: resolveToSetextUnderline,\n  tokenize: tokenizeSetextUnderline\n}\n\n/** @type {Resolver} */\nfunction resolveToSetextUnderline(events, context) {\n  // To do: resolve like `markdown-rs`.\n  let index = events.length\n  /** @type {number | undefined} */\n  let content\n  /** @type {number | undefined} */\n  let text\n  /** @type {number | undefined} */\n  let definition\n\n  // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === types.content) {\n        content = index\n        break\n      }\n\n      if (events[index][1].type === types.paragraph) {\n        text = index\n      }\n    }\n    // Exit\n    else {\n      if (events[index][1].type === types.content) {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1)\n      }\n\n      if (!definition && events[index][1].type === types.definition) {\n        definition = index\n      }\n    }\n  }\n\n  assert(text !== undefined, 'expected a `text` index to be found')\n  assert(content !== undefined, 'expected a `text` index to be found')\n  assert(events[content][2] === context, 'enter context should be same')\n  assert(\n    events[events.length - 1][2] === context,\n    'enter context should be same'\n  )\n  const heading = {\n    type: types.setextHeading,\n    start: {...events[content][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  // Change the paragraph to setext heading text.\n  events[text][1].type = types.setextHeadingText\n\n  // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context])\n    events.splice(definition + 1, 0, ['exit', events[content][1], context])\n    events[content][1].end = {...events[definition][1].end}\n  } else {\n    events[content][1] = heading\n  }\n\n  // Add the heading exit at the end.\n  events.push(['exit', heading, context])\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * At start of heading (setext) underline.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length\n    /** @type {boolean | undefined} */\n    let paragraph\n\n    assert(\n      code === codes.dash || code === codes.equalsTo,\n      'expected `=` or `-`'\n    )\n\n    // Find an opening.\n    while (index--) {\n      // Skip enter/exit of line ending, line prefix, and content.\n      // We can now either have a definition or a paragraph.\n      if (\n        self.events[index][1].type !== types.lineEnding &&\n        self.events[index][1].type !== types.linePrefix &&\n        self.events[index][1].type !== types.content\n      ) {\n        paragraph = self.events[index][1].type === types.paragraph\n        break\n      }\n    }\n\n    // To do: handle lazy/pierce like `markdown-rs`.\n    // To do: parse indent like `markdown-rs`.\n    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {\n      effects.enter(types.setextHeadingLine)\n      marker = code\n      return before(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After optional whitespace, at `-` or `=`.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    effects.enter(types.setextHeadingLineSequence)\n    return inside(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    effects.exit(types.setextHeadingLineSequence)\n\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.lineSuffix)(code)\n      : after(code)\n  }\n\n  /**\n   * After sequence, after optional whitespace.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.setextHeadingLine)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n", "import {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst characterEscapeOrReference =\n  /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nexport function decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode)\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @param {string} $1\n *   Character escape.\n * @param {string} $2\n *   Character reference.\n * @returns {string}\n *   Decoded value\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0)\n\n  if (head === codes.numberSign) {\n    const head = $2.charCodeAt(1)\n    const hex = head === codes.lowercaseX || head === codes.uppercaseX\n    return decodeNumericCharacterReference(\n      $2.slice(hex ? 2 : 1),\n      hex ? constants.numericBaseHexadecimal : constants.numericBaseDecimal\n    )\n  }\n\n  return decodeNamedCharacterReference($2) || $0\n}\n"], "mappings": ";;;;;AAkBO,IAAM;AAAA;AAAA,EAA8B;AAAA,IACzC,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,wBAAwB;AAAA,IACxB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA;AAAA,IACjB,eAAe;AAAA;AAAA,IACf,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,iBAAiB;AAAA;AAAA,IACjB,kBAAkB;AAAA;AAAA,IAClB,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,KAAK;AAAA;AAAA,IACL,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,WAAW;AAAA;AAAA,IACX,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,QAAQ;AAAA;AAAA,IACR,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,mBAAmB;AAAA;AAAA,IACnB,WAAW;AAAA;AAAA,IACX,oBAAoB;AAAA;AAAA,IACpB,OAAO;AAAA;AAAA,IACP,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,gBAAgB;AAAA;AAAA,IAChB,aAAa;AAAA;AAAA,IACb,iBAAiB;AAAA;AAAA,IACjB,OAAO;AAAA;AAAA,IACP,KAAK;AAAA;AAAA,IAEL,iBAAiB;AAAA;AAAA,IAEjB,sBAAsB;AAAA;AAAA,EACxB;AAAA;;;ACrJO,IAAM;AAAA;AAAA,EAAkC;AAAA,IAC7C,oBAAoB;AAAA;AAAA,IACpB,qBAAqB;AAAA;AAAA,IACrB,+BAA+B;AAAA;AAAA,IAC/B,uBAAuB;AAAA;AAAA,IACvB,uBAAuB;AAAA;AAAA,IACvB,oBAAoB;AAAA;AAAA,IACpB,2BAA2B;AAAA;AAAA,IAC3B,0BAA0B;AAAA;AAAA,IAC1B,kCAAkC;AAAA;AAAA,IAClC,sCAAsC;AAAA;AAAA,IACtC,gCAAgC;AAAA;AAAA,IAChC,2BAA2B;AAAA;AAAA,IAC3B,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA;AAAA,IACxB,WAAW;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,IACX,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,iBAAiB;AAAA;AAAA,IACjB,iBAAiB;AAAA;AAAA,IACjB,gBAAgB;AAAA;AAAA,IAChB,SAAS;AAAA;AAAA,IACT,mCAAmC;AAAA;AAAA,IACnC,sBAAsB;AAAA;AAAA,IACtB,sBAAsB;AAAA;AAAA,IACtB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,SAAS;AAAA;AAAA,IACT,6BAA6B;AAAA;AAAA,IAC7B,oBAAoB;AAAA;AAAA,EACtB;AAAA;;;AC9BO,IAAM;AAAA;AAAA,EAA8B;AAAA;AAAA,IAEzC,MAAM;AAAA;AAAA;AAAA,IAIN,YAAY;AAAA;AAAA;AAAA,IAIZ,YAAY;AAAA;AAAA,IAGZ,iBAAiB;AAAA;AAAA;AAAA,IAIjB,YAAY;AAAA;AAAA;AAAA,IAIZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWZ,YAAY;AAAA;AAAA,IAGZ,oBAAoB;AAAA;AAAA;AAAA,IAIpB,gBAAgB;AAAA;AAAA;AAAA,IAIhB,UAAU;AAAA;AAAA,IAGV,eAAe;AAAA;AAAA,IAGf,gBAAgB;AAAA;AAAA,IAGhB,kBAAkB;AAAA;AAAA;AAAA,IAIlB,iBAAiB;AAAA;AAAA,IAGjB,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMtB,oBAAoB;AAAA;AAAA,IAGpB,0BAA0B;AAAA;AAAA,IAG1B,iCAAiC;AAAA;AAAA,IAGjC,qCAAqC;AAAA;AAAA,IAGrC,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASzB,YAAY;AAAA;AAAA;AAAA,IAIZ,iBAAiB;AAAA;AAAA,IAGjB,yBAAyB;AAAA;AAAA;AAAA,IAIzB,qBAAqB;AAAA;AAAA;AAAA,IAIrB,qBAAqB;AAAA;AAAA,IAGrB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASf,cAAc;AAAA;AAAA;AAAA;AAAA,IAKd,UAAU;AAAA,IAEV,cAAc;AAAA;AAAA,IAGd,iBAAiB;AAAA;AAAA,IAGjB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYlB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAST,YAAY;AAAA;AAAA;AAAA;AAAA,IAKZ,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMvB,8BAA8B;AAAA;AAAA,IAG9B,oCAAoC;AAAA;AAAA;AAAA;AAAA,IAKpC,0BAA0B;AAAA;AAAA;AAAA,IAI1B,6BAA6B;AAAA;AAAA;AAAA,IAI7B,iBAAiB;AAAA;AAAA,IAGjB,uBAAuB;AAAA;AAAA;AAAA,IAIvB,uBAAuB;AAAA;AAAA,IAGvB,kBAAkB;AAAA;AAAA;AAAA,IAIlB,iBAAiB;AAAA;AAAA,IAGjB,uBAAuB;AAAA;AAAA;AAAA,IAIvB,uBAAuB;AAAA;AAAA;AAAA,IAIvB,UAAU;AAAA;AAAA,IAGV,kBAAkB;AAAA;AAAA;AAAA,IAIlB,cAAc;AAAA;AAAA,IAGd,cAAc;AAAA;AAAA;AAAA,IAId,iBAAiB;AAAA;AAAA;AAAA,IAIjB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASnB,UAAU;AAAA,IAEV,cAAc;AAAA;AAAA;AAAA,IAId,UAAU;AAAA,IAEV,cAAc;AAAA;AAAA;AAAA;AAAA,IAKd,OAAO;AAAA;AAAA;AAAA,IAIP,OAAO;AAAA;AAAA;AAAA,IAIP,WAAW;AAAA;AAAA;AAAA,IAIX,WAAW;AAAA;AAAA;AAAA,IAIX,YAAY;AAAA;AAAA,IAGZ,aAAa;AAAA;AAAA,IAGb,kBAAkB;AAAA;AAAA;AAAA,IAIlB,UAAU;AAAA;AAAA;AAAA,IAIV,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUN,WAAW;AAAA;AAAA;AAAA,IAIX,WAAW;AAAA;AAAA,IAGX,iBAAiB;AAAA;AAAA;AAAA,IAIjB,iBAAiB;AAAA;AAAA;AAAA;AAAA,IAKjB,UAAU;AAAA;AAAA;AAAA,IAIV,qBAAqB;AAAA;AAAA;AAAA;AAAA,IAKrB,4BAA4B;AAAA;AAAA,IAG5B,kCAAkC;AAAA;AAAA;AAAA,IAIlC,wBAAwB;AAAA;AAAA;AAAA,IAIxB,2BAA2B;AAAA;AAAA,IAG3B,gBAAgB;AAAA;AAAA;AAAA,IAIhB,eAAe;AAAA;AAAA,IAGf,qBAAqB;AAAA;AAAA;AAAA,IAIrB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYrB,eAAe;AAAA;AAAA;AAAA,IAIf,mBAAmB;AAAA;AAAA;AAAA,IAInB,mBAAmB;AAAA;AAAA,IAGnB,2BAA2B;AAAA;AAAA;AAAA,IAI3B,QAAQ;AAAA;AAAA,IAGR,gBAAgB;AAAA;AAAA;AAAA,IAIhB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASZ,eAAe;AAAA;AAAA,IAGf,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWvB,YAAY;AAAA;AAAA,IAEZ,kBAAkB;AAAA;AAAA,IAElB,kBAAkB;AAAA;AAAA,IAElB,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAW5B,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWb,eAAe;AAAA;AAAA,IAGf,gBAAgB;AAAA;AAAA,IAGhB,gBAAgB;AAAA;AAAA;AAAA;AAAA,IAKhB,gBAAgB;AAAA;AAAA,IAGhB,0BAA0B;AAAA;AAAA,IAG1B,eAAe;AAAA;AAAA,IAGf,eAAe;AAAA,IACf,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,EACf;AAAA;;;AC5bO,IAAM;AAAA;AAAA,EAA+B;AAAA,IAC1C,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA;;;ACpFO,SAAS,OAAOA,OAAM,OAAO,QAAQ,OAAO;AACjD,QAAM,MAAMA,MAAK;AACjB,MAAI,aAAa;AAEjB,MAAI;AAGJ,MAAI,QAAQ,GAAG;AACb,YAAQ,CAAC,QAAQ,MAAM,IAAI,MAAM;AAAA,EACnC,OAAO;AACL,YAAQ,QAAQ,MAAM,MAAM;AAAA,EAC9B;AAEA,WAAS,SAAS,IAAI,SAAS;AAG/B,MAAI,MAAM,SAAS,UAAU,oBAAoB;AAC/C,iBAAa,MAAM,KAAK,KAAK;AAC7B,eAAW,QAAQ,OAAO,MAAM;AAEhC,IAAAA,MAAK,OAAO,GAAG,UAAU;AAAA,EAC3B,OAAO;AAEL,QAAI,OAAQ,CAAAA,MAAK,OAAO,OAAO,MAAM;AAGrC,WAAO,aAAa,MAAM,QAAQ;AAChC,mBAAa,MAAM;AAAA,QACjB;AAAA,QACA,aAAa,UAAU;AAAA,MACzB;AACA,iBAAW,QAAQ,OAAO,CAAC;AAE3B,MAAAA,MAAK,OAAO,GAAG,UAAU;AAEzB,oBAAc,UAAU;AACxB,eAAS,UAAU;AAAA,IACrB;AAAA,EACF;AACF;AAkBO,SAAS,KAAKA,OAAM,OAAO;AAChC,MAAIA,MAAK,SAAS,GAAG;AACnB,WAAOA,OAAMA,MAAK,QAAQ,GAAG,KAAK;AAClC,WAAOA;AAAA,EACT;AAEA,SAAO;AACT;;;AC7EA,IAAM,iBAAiB,CAAC,EAAE;AAUnB,SAAS,kBAAkB,YAAY;AAE5C,QAAMC,OAAM,CAAC;AACb,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,WAAW,QAAQ;AAClC,oBAAgBA,MAAK,WAAW,KAAK,CAAC;AAAA,EACxC;AAEA,SAAOA;AACT;AAYA,SAAS,gBAAgBA,MAAK,WAAW;AAEvC,MAAI;AAEJ,OAAK,QAAQ,WAAW;AACtB,UAAM,QAAQ,eAAe,KAAKA,MAAK,IAAI,IAAIA,KAAI,IAAI,IAAI;AAE3D,UAAM,OAAO,UAAUA,KAAI,IAAI,IAAI,CAAC;AAEpC,UAAM,QAAQ,UAAU,IAAI;AAE5B,QAAI;AAEJ,QAAI,OAAO;AACT,WAAK,QAAQ,OAAO;AAClB,YAAI,CAAC,eAAe,KAAK,MAAM,IAAI,EAAG,MAAK,IAAI,IAAI,CAAC;AACpD,cAAM,QAAQ,MAAM,IAAI;AACxB;AAAA;AAAA,UAEE,KAAK,IAAI;AAAA,UACT,MAAM,QAAQ,KAAK,IAAI,QAAQ,QAAQ,CAAC,KAAK,IAAI,CAAC;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAaA,SAAS,WAAW,UAAUC,OAAM;AAClC,MAAI,QAAQ;AAEZ,QAAM,SAAS,CAAC;AAEhB,SAAO,EAAE,QAAQA,MAAK,QAAQ;AAE5B;AAAC,KAACA,MAAK,KAAK,EAAE,QAAQ,UAAU,WAAW,QAAQ,KAAKA,MAAK,KAAK,CAAC;AAAA,EACrE;AAEA,SAAO,UAAU,GAAG,GAAG,MAAM;AAC/B;;;AChFA,IAAM,eAAe,CAAC;AAef,SAAS,SAAS,OAAO,SAAS;AACvC,QAAM,WAAW,WAAW;AAC5B,QAAM,kBACJ,OAAO,SAAS,oBAAoB,YAChC,SAAS,kBACT;AACN,QAAM,cACJ,OAAO,SAAS,gBAAgB,YAAY,SAAS,cAAc;AAErE,SAAO,IAAI,OAAO,iBAAiB,WAAW;AAChD;AAcA,SAAS,IAAI,OAAO,iBAAiB,aAAa;AAChD,MAAI,KAAK,KAAK,GAAG;AACf,QAAI,WAAW,OAAO;AACpB,aAAO,MAAM,SAAS,UAAU,CAAC,cAAc,KAAK,MAAM;AAAA,IAC5D;AAEA,QAAI,mBAAmB,SAAS,SAAS,MAAM,KAAK;AAClD,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,cAAc,OAAO;AACvB,aAAO,IAAI,MAAM,UAAU,iBAAiB,WAAW;AAAA,IACzD;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,IAAI,OAAO,iBAAiB,WAAW;AAAA,EAChD;AAEA,SAAO;AACT;AAcA,SAAS,IAAIC,SAAQ,iBAAiB,aAAa;AAEjD,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQA,QAAO,QAAQ;AAC9B,WAAO,KAAK,IAAI,IAAIA,QAAO,KAAK,GAAG,iBAAiB,WAAW;AAAA,EACjE;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;AAUA,SAAS,KAAK,OAAO;AACnB,SAAO,QAAQ,SAAS,OAAO,UAAU,QAAQ;AACnD;;;ACvGA,IAAM,UAAU,SAAS,cAAc,GAAG;AAMnC,SAAS,8BAA8B,OAAO;AACnD,QAAMC,sBAAqB,MAAM,QAAQ;AACzC,UAAQ,YAAYA;AACpB,QAAM,YAAY,QAAQ;AAQ1B;AAAA;AAAA;AAAA,IAGE,UAAU,WAAW,UAAU,SAAS,CAAC,MAAM,MAC/C,UAAU;AAAA,IACV;AACA,WAAO;AAAA,EACT;AAMA,SAAO,cAAcA,sBAAqB,QAAQ;AACpD;;;ACnBO,SAAS,gCAAgC,OAAO,MAAM;AAC3D,QAAM,OAAO,OAAO,SAAS,OAAO,IAAI;AAExC;AAAA;AAAA,IAEE,OAAO,MAAM,MACb,SAAS,MAAM,MACd,OAAO,MAAM,MAAM,OAAO,MAAM;AAAA,IAEhC,OAAO,MAAM,SAAS,OAAO;AAAA,IAE7B,OAAO,SAAU,OAAO;AAAA,IAExB,OAAO,SAAU,OAAO;AAAA,KAExB,OAAO,WAAY,UACnB,OAAO,WAAY;AAAA;AAAA,IAGpB,OAAO;AAAA,IACP;AACA,WAAO,OAAO;AAAA,EAChB;AAEA,SAAO,OAAO,cAAc,IAAI;AAClC;;;ACpBO,SAAS,oBAAoB,OAAO;AACzC,SACE,MAEG,QAAQ,eAAe,OAAO,KAAK,EAEnC,QAAQ,UAAU,EAAE,EAOpB,YAAY,EACZ,YAAY;AAEnB;;;ACdO,IAAM,aAAa,WAAW,UAAU;AAcxC,IAAM,oBAAoB,WAAW,YAAY;AAuBjD,IAAM,aAAa,WAAW,qBAAqB;AAanD,SAAS,aAAa,MAAM;AACjC;AAAA;AAAA;AAAA,IAGE,SAAS,SAAS,OAAO,MAAM,SAAS,SAAS,MAAM;AAAA;AAE3D;AAaO,IAAM,aAAa,WAAW,IAAI;AAoBlC,IAAM,gBAAgB,WAAW,YAAY;AAe7C,IAAM,mBAAmB,WAAW,gBAAgB;AAiBpD,SAAS,mBAAmB,MAAM;AACvC,SAAO,SAAS,QAAQ,OAAO,MAAM;AACvC;AAWO,SAAS,0BAA0B,MAAM;AAC9C,SAAO,SAAS,SAAS,OAAO,MAAM,OAAO,SAAS,MAAM;AAC9D;AAiBO,SAAS,cAAc,MAAM;AAClC,SACE,SAAS,MAAM,iBACf,SAAS,MAAM,gBACf,SAAS,MAAM;AAEnB;AAuBO,IAAM,qBAAqB,WAAW,WAAC,iBAAY,GAAC;AAsBpD,IAAM,oBAAoB,WAAW,IAAI;AAUhD,SAAS,WAAW,OAAO;AACzB,SAAO;AAUP,WAAS,MAAM,MAAM;AACnB,WAAO,SAAS,QAAQ,OAAO,MAAM,MAAM,KAAK,OAAO,aAAa,IAAI,CAAC;AAAA,EAC3E;AACF;;;AC1LO,SAAS,aAAa,OAAO;AAElC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAM,OAAO,MAAM,WAAW,KAAK;AAEnC,QAAI,UAAU;AAGd,QACE,SAAS,MAAM,eACf,kBAAkB,MAAM,WAAW,QAAQ,CAAC,CAAC,KAC7C,kBAAkB,MAAM,WAAW,QAAQ,CAAC,CAAC,GAC7C;AACA,aAAO;AAAA,IACT,WAES,OAAO,KAAK;AACnB,UAAI,CAAC,oBAAoB,KAAK,OAAO,aAAa,IAAI,CAAC,GAAG;AACxD,kBAAU,OAAO,aAAa,IAAI;AAAA,MACpC;AAAA,IACF,WAES,OAAO,SAAU,OAAO,OAAQ;AACvC,YAAM,OAAO,MAAM,WAAW,QAAQ,CAAC;AAGvC,UAAI,OAAO,SAAU,OAAO,SAAU,OAAO,OAAQ;AACnD,kBAAU,OAAO,aAAa,MAAM,IAAI;AACxC,eAAO;AAAA,MACT,OAEK;AACH,kBAAU,OAAO;AAAA,MACnB;AAAA,IACF,OAEK;AACH,gBAAU,OAAO,aAAa,IAAI;AAAA,IACpC;AAEA,QAAI,SAAS;AACX,aAAO,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,mBAAmB,OAAO,CAAC;AAClE,cAAQ,QAAQ,OAAO;AACvB,gBAAU;AAAA,IACZ;AAEA,QAAI,MAAM;AACR,eAAS;AACT,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,OAAO,KAAK,EAAE,IAAI,MAAM,MAAM,KAAK;AAC5C;;;ACnFO,SAAS,aAAa,SAASC,KAAI,MAAM,KAAK;AACnD,QAAM,QAAQ,MAAM,MAAM,IAAI,OAAO;AACrC,MAAI,OAAO;AAEX,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,MAAM,IAAI;AAClB,aAAO,OAAO,IAAI;AAAA,IACpB;AAEA,WAAOA,IAAG,IAAI;AAAA,EAChB;AAGA,WAAS,OAAO,MAAM;AACpB,QAAI,cAAc,IAAI,KAAK,SAAS,OAAO;AACzC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,IAAI;AACjB,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;;;ACzCO,SAAS,kBAAkB,MAAM;AACtC,MACE,SAAS,MAAM,OACf,0BAA0B,IAAI,KAC9B,kBAAkB,IAAI,GACtB;AACA,WAAO,UAAU;AAAA,EACnB;AAEA,MAAI,mBAAmB,IAAI,GAAG;AAC5B,WAAO,UAAU;AAAA,EACnB;AACF;;;ACrBO,SAAS,WAAWC,aAAY,QAAQ,SAAS;AAEtD,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQA,YAAW,QAAQ;AAClC,UAAM,UAAUA,YAAW,KAAK,EAAE;AAElC,QAAI,WAAW,CAAC,OAAO,SAAS,OAAO,GAAG;AACxC,eAAS,QAAQ,QAAQ,OAAO;AAChC,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;;;ACjBO,IAAM,YAAY,EAAC,SAAS,MAAM,UAAU,kBAAiB;AAOpE,SAAS,kBAAkB,SAASC,KAAI,KAAK;AAC3C,SAAO;AAgBP,WAAS,MAAM,MAAM;AACnB,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,OAAO,MAAM,UAAU,EAAE,IAAI,IACnD,MAAM,IAAI;AAAA,EAChB;AAgBA,WAAS,MAAM,MAAM;AACnB,WAAO,SAAS,MAAM,OAAO,mBAAmB,IAAI,IAAIA,IAAG,IAAI,IAAI,IAAI,IAAI;AAAA,EAC7E;AACF;;;ACxCO,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AACZ;AAQA,SAAS,oBAAoB,QAAQ,SAAS;AAC5C,MAAI,QAAQ;AAEZ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAMJ,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAE9B,QACE,OAAO,KAAK,EAAE,CAAC,MAAM,WACrB,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,uBAC1B,OAAO,KAAK,EAAE,CAAC,EAAE,QACjB;AACA,aAAO;AAGP,aAAO,QAAQ;AAEb,YACE,OAAO,IAAI,EAAE,CAAC,MAAM,UACpB,OAAO,IAAI,EAAE,CAAC,EAAE,SAAS,uBACzB,OAAO,IAAI,EAAE,CAAC,EAAE;AAAA,QAEhB,QAAQ,eAAe,OAAO,IAAI,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,MAClD,QAAQ,eAAe,OAAO,KAAK,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,GACvD;AAKA,eACG,OAAO,IAAI,EAAE,CAAC,EAAE,UAAU,OAAO,KAAK,EAAE,CAAC,EAAE,WAC3C,OAAO,KAAK,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,KAAK,EAAE,CAAC,EAAE,MAAM,UAAU,KAChE,GACG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI,SACnB,OAAO,IAAI,EAAE,CAAC,EAAE,MAAM,SACtB,OAAO,KAAK,EAAE,CAAC,EAAE,IAAI,SACrB,OAAO,KAAK,EAAE,CAAC,EAAE,MAAM,UACzB,IAEF;AACA;AAAA,UACF;AAGA,gBACE,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,IAAI,EAAE,CAAC,EAAE,MAAM,SAAS,KAC5D,OAAO,KAAK,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,KAAK,EAAE,CAAC,EAAE,MAAM,SAAS,IAC1D,IACA;AAEN,gBAAM,QAAQ,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAG;AACrC,gBAAM,MAAM,EAAC,GAAG,OAAO,KAAK,EAAE,CAAC,EAAE,MAAK;AACtC,oBAAU,OAAO,CAAC,GAAG;AACrB,oBAAU,KAAK,GAAG;AAElB,4BAAkB;AAAA,YAChB,MAAM,MAAM,IAAI,MAAM,iBAAiB,MAAM;AAAA,YAC7C;AAAA,YACA,KAAK,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAG;AAAA,UAC9B;AACA,4BAAkB;AAAA,YAChB,MAAM,MAAM,IAAI,MAAM,iBAAiB,MAAM;AAAA,YAC7C,OAAO,EAAC,GAAG,OAAO,KAAK,EAAE,CAAC,EAAE,MAAK;AAAA,YACjC;AAAA,UACF;AACA,iBAAO;AAAA,YACL,MAAM,MAAM,IAAI,MAAM,aAAa,MAAM;AAAA,YACzC,OAAO,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAG;AAAA,YAC9B,KAAK,EAAC,GAAG,OAAO,KAAK,EAAE,CAAC,EAAE,MAAK;AAAA,UACjC;AACA,kBAAQ;AAAA,YACN,MAAM,MAAM,IAAI,MAAM,SAAS,MAAM;AAAA,YACrC,OAAO,EAAC,GAAG,gBAAgB,MAAK;AAAA,YAChC,KAAK,EAAC,GAAG,gBAAgB,IAAG;AAAA,UAC9B;AAEA,iBAAO,IAAI,EAAE,CAAC,EAAE,MAAM,EAAC,GAAG,gBAAgB,MAAK;AAC/C,iBAAO,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,gBAAgB,IAAG;AAEhD,uBAAa,CAAC;AAGd,cAAI,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,IAAI,EAAE,CAAC,EAAE,MAAM,QAAQ;AAC7D,yBAAa,KAAK,YAAY;AAAA,cAC5B,CAAC,SAAS,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO;AAAA,cAClC,CAAC,QAAQ,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO;AAAA,YACnC,CAAC;AAAA,UACH;AAGA,uBAAa,KAAK,YAAY;AAAA,YAC5B,CAAC,SAAS,OAAO,OAAO;AAAA,YACxB,CAAC,SAAS,iBAAiB,OAAO;AAAA,YAClC,CAAC,QAAQ,iBAAiB,OAAO;AAAA,YACjC,CAAC,SAAS,MAAM,OAAO;AAAA,UACzB,CAAC;AAGD;AAAA,YACE,QAAQ,OAAO,WAAW,WAAW;AAAA,YACrC;AAAA,UACF;AAGA,uBAAa;AAAA,YACX;AAAA,YACA;AAAA,cACE,QAAQ,OAAO,WAAW,WAAW;AAAA,cACrC,OAAO,MAAM,OAAO,GAAG,KAAK;AAAA,cAC5B;AAAA,YACF;AAAA,UACF;AAGA,uBAAa,KAAK,YAAY;AAAA,YAC5B,CAAC,QAAQ,MAAM,OAAO;AAAA,YACtB,CAAC,SAAS,iBAAiB,OAAO;AAAA,YAClC,CAAC,QAAQ,iBAAiB,OAAO;AAAA,YACjC,CAAC,QAAQ,OAAO,OAAO;AAAA,UACzB,CAAC;AAGD,cAAI,OAAO,KAAK,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,KAAK,EAAE,CAAC,EAAE,MAAM,QAAQ;AAC/D,qBAAS;AACT,yBAAa,KAAK,YAAY;AAAA,cAC5B,CAAC,SAAS,OAAO,KAAK,EAAE,CAAC,GAAG,OAAO;AAAA,cACnC,CAAC,QAAQ,OAAO,KAAK,EAAE,CAAC,GAAG,OAAO;AAAA,YACpC,CAAC;AAAA,UACH,OAAO;AACL,qBAAS;AAAA,UACX;AAEA,iBAAO,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,UAAU;AAErD,kBAAQ,OAAO,WAAW,SAAS,SAAS;AAC5C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,UAAQ;AAER,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,QAAI,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,qBAAqB;AACjD,aAAO,KAAK,EAAE,CAAC,EAAE,OAAO;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAOA,SAAS,kBAAkB,SAASC,KAAI;AACtC,QAAM,mBAAmB,KAAK,OAAO,WAAW,iBAAiB;AACjE,QAAMC,YAAW,KAAK;AACtB,QAAM,SAAS,kBAAkBA,SAAQ;AAGzC,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB;AAAA,MACE,SAAS,MAAM,YAAY,SAAS,MAAM;AAAA,MAC1C;AAAA,IACF;AACA,aAAS;AACT,YAAQ,MAAM,mBAAmB;AACjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,QAAQ,KAAK,mBAAmB;AAG9C,UAAM,QAAQ,kBAAkB,IAAI;AAGpC,OAAO,kBAAkB,6CAA6C;AAEtE,UAAM,OACJ,CAAC,SACA,UAAU,UAAU,6BAA6B,UAClD,iBAAiB,SAAS,IAAI;AAChC,UAAM,QACJ,CAAC,UACA,WAAW,UAAU,6BAA6B,SACnD,iBAAiB,SAASA,SAAQ;AAEpC,UAAM,QAAQ;AAAA,MACZ,WAAW,MAAM,WAAW,OAAO,SAAS,UAAU,CAAC;AAAA,IACzD;AACA,UAAM,SAAS;AAAA,MACb,WAAW,MAAM,WAAW,QAAQ,UAAU,SAAS,CAAC;AAAA,IAC1D;AACA,WAAOD,IAAG,IAAI;AAAA,EAChB;AACF;AAeA,SAAS,UAAU,OAAO,QAAQ;AAChC,QAAM,UAAU;AAChB,QAAM,UAAU;AAChB,QAAM,gBAAgB;AACxB;;;ACxRO,IAAM,WAAW,EAAC,MAAM,YAAY,UAAU,iBAAgB;AAOrE,SAAS,iBAAiB,SAASE,KAAI,KAAK;AAC1C,MAAI,OAAO;AAEX,SAAO;AAcP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,UAAU,cAAc;AAC9C,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,cAAc;AAClC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,cAAc;AACjC,YAAQ,MAAM,MAAM,gBAAgB;AACpC,WAAO;AAAA,EACT;AAcA,WAAS,KAAK,MAAM;AAClB,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,QAAQ;AACzB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAcA,WAAS,mBAAmB,MAAM;AAEhC,QACE,SAAS,MAAM,YACf,SAAS,MAAM,QACf,SAAS,MAAM,OACf,kBAAkB,IAAI,GACtB;AAEA,aAAO;AACP,aAAO,yBAAyB,IAAI;AAAA,IACtC;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAcA,WAAS,yBAAyB,MAAM;AACtC,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AACP,aAAO;AAAA,IACT;AAGA,SACG,SAAS,MAAM,YACd,SAAS,MAAM,QACf,SAAS,MAAM,OACf,kBAAkB,IAAI,MACxB,SAAS,UAAU,uBACnB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAYA,WAAS,UAAU,MAAM;AACvB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,KAAK,MAAM,gBAAgB;AACnC,cAAQ,MAAM,MAAM,cAAc;AAClC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,cAAc;AACjC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA;AAAA,IACT;AAGA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,YACf,aAAa,IAAI,GACjB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,QAAQ;AACzB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,kBAAkB,IAAI,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI;AAAA,EAC9D;AAYA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,KAAK;AACtB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AACP,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,aAAa;AAE9B,cAAQ,KAAK,MAAM,gBAAgB,EAAE,OAAO,MAAM;AAClD,cAAQ,MAAM,MAAM,cAAc;AAClC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,cAAc;AACjC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA;AAAA,IACT;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAcA,WAAS,WAAW,MAAM;AAExB,SACG,SAAS,MAAM,QAAQ,kBAAkB,IAAI,MAC9C,SAAS,UAAU,uBACnB;AACA,YAAM,OAAO,SAAS,MAAM,OAAO,aAAa;AAChD,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AC5PO,IAAM,aAAa;AAAA,EACxB,cAAc,EAAC,UAAU,+BAA8B;AAAA,EACvD;AAAA,EACA,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASC,KAAI,KAAK;AACjD,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,aAAa;AAC9B,YAAM,QAAQ,KAAK;AAEnB,SAAO,OAAO,sDAAsD;AAEpE,UAAI,CAAC,MAAM,MAAM;AACf,gBAAQ,MAAM,MAAM,YAAY,EAAC,YAAY,KAAI,CAAC;AAClD,cAAM,OAAO;AAAA,MACf;AAEA,cAAQ,MAAM,MAAM,gBAAgB;AACpC,cAAQ,MAAM,MAAM,gBAAgB;AACpC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,gBAAgB;AACnC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,MAAM,MAAM;AACnB,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,MAAM,MAAM,0BAA0B;AAC9C,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,0BAA0B;AAC7C,cAAQ,KAAK,MAAM,gBAAgB;AACnC,aAAOA;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,gBAAgB;AACnC,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAeA,SAAS,+BAA+B,SAASA,KAAI,KAAK;AACxD,QAAM,OAAO;AAEb,SAAO;AAeP,WAAS,UAAU,MAAM;AACvB,QAAI,cAAc,IAAI,GAAG;AAEvB;AAAA,QACE,KAAK,OAAO,WAAW,QAAQ;AAAA,QAC/B;AAAA,MACF;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,MAChB,EAAE,IAAI;AAAA,IACR;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAeA,WAAS,WAAW,MAAM;AACxB,WAAO,QAAQ,QAAQ,YAAYA,KAAI,GAAG,EAAE,IAAI;AAAA,EAClD;AACF;AAGA,SAAS,KAAK,SAAS;AACrB,UAAQ,KAAK,MAAM,UAAU;AAC/B;;;ACnJO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASC,KAAI,KAAK;AACjD,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,WAAW,eAAe;AAChD,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,MAAM,MAAM,YAAY;AAChC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,YAAY;AAC/B,WAAO;AAAA,EACT;AAYA,WAAS,OAAO,MAAM;AAEpB,QAAI,iBAAiB,IAAI,GAAG;AAC1B,cAAQ,MAAM,MAAM,oBAAoB;AACxC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,oBAAoB;AACvC,cAAQ,KAAK,MAAM,eAAe;AAClC,aAAOA;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AChDO,IAAM,qBAAqB;AAAA,EAChC,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,2BAA2B,SAASC,KAAI,KAAK;AACpD,QAAM,OAAO;AACb,MAAI,OAAO;AAEX,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAgBP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,WAAW,cAAc;AAC/C,YAAQ,MAAM,MAAM,kBAAkB;AACtC,YAAQ,MAAM,MAAM,wBAAwB;AAC5C,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,wBAAwB;AAC3C,WAAO;AAAA,EACT;AAiBA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,YAAY;AAC7B,cAAQ,MAAM,MAAM,+BAA+B;AACnD,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,+BAA+B;AAClD,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM,MAAM,uBAAuB;AAC3C,UAAM,UAAU;AAChB,WAAO;AACP,WAAO,MAAM,IAAI;AAAA,EACnB;AAcA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,cAAc,SAAS,MAAM,YAAY;AAC1D,cAAQ,MAAM,MAAM,mCAAmC;AACvD,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,mCAAmC;AACtD,cAAQ,MAAM,MAAM,uBAAuB;AAC3C,YAAM,UAAU;AAChB,aAAO;AACP,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM,MAAM,uBAAuB;AAC3C,UAAM,UAAU;AAChB,WAAO;AACP,WAAO,MAAM,IAAI;AAAA,EACnB;AAmBA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,aAAa,MAAM;AACpC,YAAM,QAAQ,QAAQ,KAAK,MAAM,uBAAuB;AAExD,UACE,SAAS,qBACT,CAAC,8BAA8B,KAAK,eAAe,KAAK,CAAC,GACzD;AACA,eAAO,IAAI,IAAI;AAAA,MACjB;AAIA,cAAQ,MAAM,MAAM,wBAAwB;AAC5C,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,wBAAwB;AAC3C,cAAQ,KAAK,MAAM,kBAAkB;AACrC,aAAOA;AAAA,IACT;AAEA,QAAI,KAAK,IAAI,KAAK,SAAS,KAAK;AAC9B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;ACrJA,IAAM,sBAAsB;AAAA,EAC1B,SAAS;AAAA,EACT,UAAU;AACZ;AAGO,IAAM,aAAa;AAAA,EACxB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,mBAAmB,SAASC,KAAI,KAAK;AAC5C,QAAM,OAAO;AAEb,QAAM,aAAa,EAAC,SAAS,MAAM,UAAU,mBAAkB;AAC/D,MAAI,gBAAgB;AACpB,MAAI,WAAW;AAEf,MAAI;AAEJ,SAAO;AAcP,WAAS,MAAM,MAAM;AAEnB,WAAO,mBAAmB,IAAI;AAAA,EAChC;AAcA,WAAS,mBAAmB,MAAM;AAChC;AAAA,MACE,SAAS,MAAM,eAAe,SAAS,MAAM;AAAA,MAC7C;AAAA,IACF;AAEA,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,oBACE,QAAQ,KAAK,CAAC,EAAE,SAAS,MAAM,aAC3B,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,SACtC;AAEN,aAAS;AACT,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,MAAM,MAAM,uBAAuB;AAC3C,WAAO,aAAa,IAAI;AAAA,EAC1B;AAcA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,QAAQ;AACnB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,UAAU,2BAA2B;AAClD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,KAAK,MAAM,uBAAuB;AAC1C,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,YAAY,MAAM,UAAU,EAAE,IAAI,IACxD,WAAW,IAAI;AAAA,EACrB;AAcA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,eAAe;AAClC,aAAO,KAAK,YACRA,IAAG,IAAI,IACP,QAAQ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI;AAAA,IACpE;AAEA,YAAQ,MAAM,MAAM,mBAAmB;AACvC,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,KAAK,IAAI;AAAA,EAClB;AAcA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,mBAAmB;AACtC,aAAO,WAAW,IAAI;AAAA,IACxB;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,mBAAmB;AACtC,aAAO,aAAa,SAAS,YAAY,MAAM,UAAU,EAAE,IAAI;AAAA,IACjE;AAEA,QAAI,SAAS,MAAM,eAAe,SAAS,QAAQ;AACjD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAcA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,WAAW,IAAI;AAAA,IACxB;AAEA,YAAQ,MAAM,MAAM,mBAAmB;AACvC,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,KAAK,IAAI;AAAA,EAClB;AAcA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,mBAAmB;AACtC,aAAO,WAAW,IAAI;AAAA,IACxB;AAEA,QAAI,SAAS,MAAM,eAAe,SAAS,QAAQ;AACjD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAeA,WAAS,eAAe,MAAM;AAC5B,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,WAAO,QAAQ,QAAQ,YAAY,OAAO,aAAa,EAAE,IAAI;AAAA,EAC/D;AAcA,WAAS,cAAc,MAAM;AAC3B,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAcA,WAAS,aAAa,MAAM;AAC1B,WAAO,gBAAgB,KAAK,cAAc,IAAI,IAC1C;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB,EAAE,IAAI,IACN,mBAAmB,IAAI;AAAA,EAC7B;AAcA,WAAS,mBAAmB,MAAM;AAChC,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,QAAQ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI;AAAA,IACvE;AAEA,YAAQ,MAAM,MAAM,aAAa;AACjC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAcA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAO,mBAAmB,IAAI;AAAA,IAChC;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAcA,WAAS,MAAM,MAAM;AACnB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAOA,IAAG,IAAI;AAAA,EAChB;AAOA,WAAS,mBAAmBC,UAASD,KAAIE,MAAK;AAC5C,QAAI,OAAO;AAEX,WAAO;AAOP,aAAS,YAAY,MAAM;AACzB,SAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,MAAAD,SAAQ,MAAM,MAAM,UAAU;AAC9B,MAAAA,SAAQ,QAAQ,IAAI;AACpB,MAAAA,SAAQ,KAAK,MAAM,UAAU;AAC7B,aAAOE;AAAA,IACT;AAcA,aAASA,OAAM,MAAM;AAEnB;AAAA,QACE,KAAK,OAAO,WAAW,QAAQ;AAAA,QAC/B;AAAA,MACF;AAGA,MAAAF,SAAQ,MAAM,MAAM,eAAe;AACnC,aAAO,cAAc,IAAI,IACrB;AAAA,QACEA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,MAChB,EAAE,IAAI,IACN,oBAAoB,IAAI;AAAA,IAC9B;AAcA,aAAS,oBAAoB,MAAM;AACjC,UAAI,SAAS,QAAQ;AACnB,QAAAA,SAAQ,MAAM,MAAM,uBAAuB;AAC3C,eAAO,cAAc,IAAI;AAAA,MAC3B;AAEA,aAAOC,KAAI,IAAI;AAAA,IACjB;AAcA,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,QAAQ;AACnB;AACA,QAAAD,SAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,UAAU;AACpB,QAAAA,SAAQ,KAAK,MAAM,uBAAuB;AAC1C,eAAO,cAAc,IAAI,IACrB,aAAaA,UAAS,oBAAoB,MAAM,UAAU,EAAE,IAAI,IAChE,mBAAmB,IAAI;AAAA,MAC7B;AAEA,aAAOC,KAAI,IAAI;AAAA,IACjB;AAcA,aAAS,mBAAmB,MAAM;AAChC,UAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,QAAAD,SAAQ,KAAK,MAAM,eAAe;AAClC,eAAOD,IAAG,IAAI;AAAA,MAChB;AAEA,aAAOE,KAAI,IAAI;AAAA,IACjB;AAAA,EACF;AACF;AAOA,SAAS,4BAA4B,SAASF,KAAI,KAAK;AACrD,QAAM,OAAO;AAEb,SAAO;AAOP,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAOA,WAAS,UAAU,MAAM;AACvB,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAIA,IAAG,IAAI;AAAA,EAChE;AACF;;;AClfO,IAAM,eAAe;AAAA,EAC1B,MAAM;AAAA,EACN,UAAU;AACZ;AAGA,IAAM,eAAe,EAAC,SAAS,MAAM,UAAU,qBAAoB;AAOnE,SAAS,qBAAqB,SAASI,KAAI,KAAK;AAC9C,QAAM,OAAO;AACb,SAAO;AAgBP,WAAS,MAAM,MAAM;AAEnB,OAAO,cAAc,IAAI,CAAC;AAC1B,YAAQ,MAAM,MAAM,YAAY;AAGhC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,UAAU,UAAU;AAAA,IACtB,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,YAAY,MAAM;AACzB,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QACL,KAAK,CAAC,EAAE,SAAS,MAAM,cACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,UAAU,UAAU,UACxD,QAAQ,IAAI,IACZ,IAAI,IAAI;AAAA,EACd;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,MAAM,IAAI;AAAA,IACnB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,aAAO,QAAQ,QAAQ,cAAc,SAAS,KAAK,EAAE,IAAI;AAAA,IAC3D;AAEA,YAAQ,MAAM,MAAM,aAAa;AACjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAGA,WAAS,MAAM,MAAM;AACnB,YAAQ,KAAK,MAAM,YAAY;AAI/B,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAOA,SAAS,qBAAqB,SAASA,KAAI,KAAK;AAC9C,QAAM,OAAO;AAEb,SAAOC;AAaP,WAASA,cAAa,MAAM;AAG1B,QAAI,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG;AACrC,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAOA;AAAA,IACT;AAQA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,UAAU,UAAU;AAAA,IACtB,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,YAAY,MAAM;AACzB,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QACL,KAAK,CAAC,EAAE,SAAS,MAAM,cACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,UAAU,UAAU,UACxDD,IAAG,IAAI,IACP,mBAAmB,IAAI,IACrBC,cAAa,IAAI,IACjB,IAAI,IAAI;AAAA,EAChB;AACF;;;ACxLO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AACZ;AAIA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,gBAAgB,OAAO,SAAS;AACpC,MAAI,iBAAiB;AAErB,MAAI;AAEJ,MAAI;AAGJ,OACG,OAAO,cAAc,EAAE,CAAC,EAAE,SAAS,MAAM,cACxC,OAAO,cAAc,EAAE,CAAC,EAAE,SAAS,aACpC,OAAO,aAAa,EAAE,CAAC,EAAE,SAAS,MAAM,cACvC,OAAO,aAAa,EAAE,CAAC,EAAE,SAAS,UACpC;AACA,YAAQ;AAGR,WAAO,EAAE,QAAQ,eAAe;AAC9B,UAAI,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cAAc;AAEhD,eAAO,cAAc,EAAE,CAAC,EAAE,OAAO,MAAM;AACvC,eAAO,aAAa,EAAE,CAAC,EAAE,OAAO,MAAM;AACtC,0BAAkB;AAClB,yBAAiB;AACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,UAAQ,iBAAiB;AACzB;AAEA,SAAO,EAAE,SAAS,eAAe;AAC/B,QAAI,UAAU,QAAW;AACvB,UACE,UAAU,iBACV,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAChC;AACA,gBAAQ;AAAA,MACV;AAAA,IACF,WACE,UAAU,iBACV,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAChC;AACA,aAAO,KAAK,EAAE,CAAC,EAAE,OAAO,MAAM;AAE9B,UAAI,UAAU,QAAQ,GAAG;AACvB,eAAO,KAAK,EAAE,CAAC,EAAE,MAAM,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC5C,eAAO,OAAO,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAC1C,yBAAiB,QAAQ,QAAQ;AACjC,gBAAQ,QAAQ;AAAA,MAClB;AAEA,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,SAAO;AACT;AAOA,SAAS,SAAS,MAAM;AAEtB,SACE,SAAS,MAAM,eACf,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM;AAE1D;AAOA,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AACb,MAAI,WAAW;AAEf,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAcP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,aAAa,kBAAkB;AACrD,OAAO,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG,2BAA2B;AACtE,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,gBAAgB;AACpC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,gBAAgB;AACnC,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,QAAQ,MAAM;AAErB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAKA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,MAAM,OAAO;AACrB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,OAAO;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,MAAM,MAAM,gBAAgB;AAC5C,aAAO;AACP,aAAO,cAAc,IAAI;AAAA,IAC3B;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAGA,YAAQ,MAAM,MAAM,YAAY;AAChC,WAAO,KAAK,IAAI;AAAA,EAClB;AAYA,WAAS,KAAK,MAAM;AAClB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,eACf,mBAAmB,IAAI,GACvB;AACA,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,cAAc,MAAM;AAE3B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,UAAU;AACrB,cAAQ,KAAK,MAAM,gBAAgB;AACnC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA,IAAG,IAAI;AAAA,IAChB;AAGA,UAAM,OAAO,MAAM;AACnB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;;;ACjOO,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,YAAY,SAAS;AAEnB,SAAK,OAAO,UAAU,CAAC,GAAG,OAAO,IAAI,CAAC;AAEtC,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,OAAO;AACT,QAAI,QAAQ,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,MAAM,QAAQ;AAC9D,YAAM,IAAI;AAAA,QACR,0BACE,QACA,oCACC,KAAK,KAAK,SAAS,KAAK,MAAM,UAC/B;AAAA,MACJ;AAAA,IACF;AAEA,QAAI,QAAQ,KAAK,KAAK,OAAQ,QAAO,KAAK,KAAK,KAAK;AACpD,WAAO,KAAK,MAAM,KAAK,MAAM,SAAS,QAAQ,KAAK,KAAK,SAAS,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,SAAS,KAAK,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ;AACN,SAAK,UAAU,CAAC;AAChB,WAAO,KAAK,MAAM,IAAI;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,OAAO,KAAK;AAEhB,UAAM,OACJ,QAAQ,QAAQ,QAAQ,SAAY,OAAO,oBAAoB;AAEjE,QAAI,OAAO,KAAK,KAAK,QAAQ;AAC3B,aAAO,KAAK,KAAK,MAAM,OAAO,IAAI;AAAA,IACpC;AAEA,QAAI,QAAQ,KAAK,KAAK,QAAQ;AAC5B,aAAO,KAAK,MACT;AAAA,QACC,KAAK,MAAM,SAAS,OAAO,KAAK,KAAK;AAAA,QACrC,KAAK,MAAM,SAAS,QAAQ,KAAK,KAAK;AAAA,MACxC,EACC,QAAQ;AAAA,IACb;AAEA,WAAO,KAAK,KACT,MAAM,KAAK,EACX;AAAA,MACC,KAAK,MAAM,MAAM,KAAK,MAAM,SAAS,OAAO,KAAK,KAAK,MAAM,EAAE,QAAQ;AAAA,IACxE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,OAAO,OAAO,aAAa,OAAO;AAEhC,UAAM,QAAQ,eAAe;AAE7B,SAAK,UAAU,KAAK,MAAM,KAAK,CAAC;AAChC,UAAM,UAAU,KAAK,MAAM;AAAA,MACzB,KAAK,MAAM,SAAS;AAAA,MACpB,OAAO;AAAA,IACT;AACA,QAAI,MAAO,aAAY,KAAK,MAAM,KAAK;AACvC,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM;AACJ,SAAK,UAAU,OAAO,iBAAiB;AACvC,WAAO,KAAK,KAAK,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,MAAM;AACT,SAAK,UAAU,OAAO,iBAAiB;AACvC,SAAK,KAAK,KAAK,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,OAAO;AACd,SAAK,UAAU,OAAO,iBAAiB;AACvC,gBAAY,KAAK,MAAM,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,MAAM;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,OAAO;AACjB,SAAK,UAAU,CAAC;AAChB,gBAAY,KAAK,OAAO,MAAM,QAAQ,CAAC;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,UAAU,GAAG;AACX,QACE,MAAM,KAAK,KAAK,UACf,IAAI,KAAK,KAAK,UAAU,KAAK,MAAM,WAAW,KAC9C,IAAI,KAAK,KAAK,KAAK,WAAW;AAE/B;AACF,QAAI,IAAI,KAAK,KAAK,QAAQ;AAExB,YAAM,UAAU,KAAK,KAAK,OAAO,GAAG,OAAO,iBAAiB;AAC5D,kBAAY,KAAK,OAAO,QAAQ,QAAQ,CAAC;AAAA,IAC3C,OAAO;AAEL,YAAM,UAAU,KAAK,MAAM;AAAA,QACzB,KAAK,KAAK,SAAS,KAAK,MAAM,SAAS;AAAA,QACvC,OAAO;AAAA,MACT;AACA,kBAAY,KAAK,MAAM,QAAQ,QAAQ,CAAC;AAAA,IAC1C;AAAA,EACF;AACF;AAcA,SAAS,YAAYC,OAAM,OAAO;AAEhC,MAAI,aAAa;AAEjB,MAAI,MAAM,SAAS,UAAU,oBAAoB;AAC/C,IAAAA,MAAK,KAAK,GAAG,KAAK;AAAA,EACpB,OAAO;AACL,WAAO,aAAa,MAAM,QAAQ;AAChC,MAAAA,MAAK;AAAA,QACH,GAAG,MAAM,MAAM,YAAY,aAAa,UAAU,kBAAkB;AAAA,MACtE;AACA,oBAAc,UAAU;AAAA,IAC1B;AAAA,EACF;AACF;;;AC1QO,SAAS,YAAY,aAAa;AAEvC,QAAM,QAAQ,CAAC;AACf,MAAI,QAAQ;AAEZ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AACJ,QAAM,SAAS,IAAI,aAAa,WAAW;AAE3C,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,WAAO,SAAS,OAAO;AACrB,cAAQ,MAAM,KAAK;AAAA,IACrB;AAEA,YAAQ,OAAO,IAAI,KAAK;AAIxB,QACE,SACA,MAAM,CAAC,EAAE,SAAS,MAAM,aACxB,OAAO,IAAI,QAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,gBACxC;AACA,SAAO,MAAM,CAAC,EAAE,YAAY,oCAAoC;AAChE,kBAAY,MAAM,CAAC,EAAE,WAAW;AAChC,mBAAa;AAEb,UACE,aAAa,UAAU,UACvB,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,iBACxC;AACA,sBAAc;AAAA,MAChB;AAEA,UACE,aAAa,UAAU,UACvB,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,SACxC;AACA,eAAO,EAAE,aAAa,UAAU,QAAQ;AACtC,cAAI,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,SAAS;AACnD;AAAA,UACF;AAEA,cAAI,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,WAAW;AACrD,sBAAU,UAAU,EAAE,CAAC,EAAE,8BAA8B;AACvD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,MAAM,CAAC,MAAM,SAAS;AACxB,UAAI,MAAM,CAAC,EAAE,aAAa;AACxB,eAAO,OAAO,OAAO,WAAW,QAAQ,KAAK,CAAC;AAC9C,gBAAQ,MAAM,KAAK;AACnB,eAAO;AAAA,MACT;AAAA,IACF,WAES,MAAM,CAAC,EAAE,YAAY;AAC5B,mBAAa;AACb,kBAAY;AAEZ,aAAO,cAAc;AACnB,qBAAa,OAAO,IAAI,UAAU;AAElC,YACE,WAAW,CAAC,EAAE,SAAS,MAAM,cAC7B,WAAW,CAAC,EAAE,SAAS,MAAM,iBAC7B;AACA,cAAI,WAAW,CAAC,MAAM,SAAS;AAC7B,gBAAI,WAAW;AACb,qBAAO,IAAI,SAAS,EAAE,CAAC,EAAE,OAAO,MAAM;AAAA,YACxC;AAEA,uBAAW,CAAC,EAAE,OAAO,MAAM;AAC3B,wBAAY;AAAA,UACd;AAAA,QACF,WACE,WAAW,CAAC,EAAE,SAAS,MAAM,cAC7B,WAAW,CAAC,EAAE,SAAS,MAAM,gBAC7B;AAAA,QAEF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW;AAEb,cAAM,CAAC,EAAE,MAAM,EAAC,GAAG,OAAO,IAAI,SAAS,EAAE,CAAC,EAAE,MAAK;AAGjD,qBAAa,OAAO,MAAM,WAAW,KAAK;AAC1C,mBAAW,QAAQ,KAAK;AACxB,eAAO,OAAO,WAAW,QAAQ,YAAY,GAAG,UAAU;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAGA,SAAO,aAAa,GAAG,OAAO,mBAAmB,OAAO,MAAM,CAAC,CAAC;AAChE,SAAO,CAAC;AACV;AAYA,SAAS,WAAW,QAAQ,YAAY;AACtC,QAAM,QAAQ,OAAO,IAAI,UAAU,EAAE,CAAC;AACtC,QAAM,UAAU,OAAO,IAAI,UAAU,EAAE,CAAC;AACxC,MAAI,gBAAgB,aAAa;AAEjC,QAAM,iBAAiB,CAAC;AACxB,KAAO,MAAM,aAAa,qCAAqC;AAE/D,MAAI,YAAY,MAAM;AAEtB,MAAI,CAAC,WAAW;AACd,gBAAY,QAAQ,OAAO,MAAM,WAAW,EAAE,MAAM,KAAK;AAEzD,QAAI,MAAM,0BAA0B;AAClC,gBAAU,2BAA2B;AAAA,IACvC;AAAA,EACF;AAEA,QAAM,cAAc,UAAU;AAE9B,QAAM,QAAQ,CAAC;AAEf,QAAM,OAAO,CAAC;AAEd,MAAI;AAEJ,MAAIC;AACJ,MAAI,QAAQ;AAEZ,MAAI,UAAU;AACd,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,QAAM,SAAS,CAAC,KAAK;AAIrB,SAAO,SAAS;AAEd,WAAO,OAAO,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,SAAS;AAAA,IAEnD;AAEA;AAAA,MACE,CAACA,aAAY,QAAQ,aAAaA;AAAA,MAClC;AAAA,IACF;AACA,OAAO,CAACA,aAAYA,UAAS,SAAS,SAAS,wBAAwB;AAEvE,mBAAe,KAAK,aAAa;AAEjC,QAAI,CAAC,QAAQ,YAAY;AACvB,eAAS,QAAQ,YAAY,OAAO;AAEpC,UAAI,CAAC,QAAQ,MAAM;AACjB,eAAO,KAAK,MAAM,GAAG;AAAA,MACvB;AAEA,UAAIA,WAAU;AACZ,kBAAU,WAAW,QAAQ,KAAK;AAAA,MACpC;AAEA,UAAI,QAAQ,6BAA6B;AACvC,kBAAU,qCAAqC;AAAA,MACjD;AAEA,gBAAU,MAAM,MAAM;AAEtB,UAAI,QAAQ,6BAA6B;AACvC,kBAAU,qCAAqC;AAAA,MACjD;AAAA,IACF;AAGA,IAAAA,YAAW;AACX,cAAU,QAAQ;AAAA,EACpB;AAIA,YAAU;AAEV,SAAO,EAAE,QAAQ,YAAY,QAAQ;AACnC;AAAA;AAAA,MAEE,YAAY,KAAK,EAAE,CAAC,MAAM,UAC1B,YAAY,QAAQ,CAAC,EAAE,CAAC,MAAM,WAC9B,YAAY,KAAK,EAAE,CAAC,EAAE,SAAS,YAAY,QAAQ,CAAC,EAAE,CAAC,EAAE,QACzD,YAAY,KAAK,EAAE,CAAC,EAAE,MAAM,SAAS,YAAY,KAAK,EAAE,CAAC,EAAE,IAAI;AAAA,MAC/D;AACA,SAAO,SAAS,0BAA0B;AAC1C,cAAQ,QAAQ;AAChB,aAAO,KAAK,KAAK;AAEjB,cAAQ,aAAa;AACrB,cAAQ,WAAW;AACnB,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAGA,YAAU,SAAS,CAAC;AAKpB,MAAI,SAAS;AAEX,YAAQ,aAAa;AACrB,YAAQ,WAAW;AACnB,OAAO,CAAC,QAAQ,MAAM,wBAAwB;AAAA,EAChD,OAAO;AACL,WAAO,IAAI;AAAA,EACb;AAIA,UAAQ,OAAO;AAEf,SAAO,SAAS;AACd,UAAM,QAAQ,YAAY,MAAM,OAAO,KAAK,GAAG,OAAO,QAAQ,CAAC,CAAC;AAChE,UAAMC,SAAQ,eAAe,IAAI;AACjC,OAAOA,WAAU,QAAW,yCAAyC;AACrE,UAAM,KAAK,CAACA,QAAOA,SAAQ,MAAM,SAAS,CAAC,CAAC;AAC5C,WAAO,OAAOA,QAAO,GAAG,KAAK;AAAA,EAC/B;AAEA,QAAM,QAAQ;AACd,UAAQ;AAER,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,SAAK,SAAS,MAAM,KAAK,EAAE,CAAC,CAAC,IAAI,SAAS,MAAM,KAAK,EAAE,CAAC;AACxD,cAAU,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI;AAAA,EAChD;AAEA,SAAO;AACT;;;ACtQO,IAAM,UAAU,EAAC,SAAS,gBAAgB,UAAU,gBAAe;AAG1E,IAAM,wBAAwB,EAAC,SAAS,MAAM,UAAU,qBAAoB;AAQ5E,SAAS,eAAe,QAAQ;AAC9B,cAAY,MAAM;AAClB,SAAO;AACT;AAOA,SAAS,gBAAgB,SAASC,KAAI;AAEpC,MAAIC;AAEJ,SAAO;AAYP,WAAS,WAAW,MAAM;AACxB;AAAA,MACE,SAAS,MAAM,OAAO,CAAC,mBAAmB,IAAI;AAAA,MAC9C;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,OAAO;AAC3B,IAAAA,YAAW,QAAQ,MAAM,MAAM,cAAc;AAAA,MAC3C,aAAa,UAAU;AAAA,IACzB,CAAC;AACD,WAAO,YAAY,IAAI;AAAA,EACzB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,WAAW,IAAI;AAAA,IACxB;AAIA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,IAAI;AAAA,IACR;AAGA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAOA,WAAS,WAAW,MAAM;AACxB,YAAQ,KAAK,MAAM,YAAY;AAC/B,YAAQ,KAAK,MAAM,OAAO;AAC1B,WAAOD,IAAG,IAAI;AAAA,EAChB;AAOA,WAAS,gBAAgB,MAAM;AAC7B,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,YAAY;AAC/B,OAAOC,WAAU,yBAAyB;AAC1C,IAAAA,UAAS,OAAO,QAAQ,MAAM,MAAM,cAAc;AAAA,MAChD,aAAa,UAAU;AAAA,MACvB,UAAAA;AAAA,IACF,CAAC;AACD,IAAAA,YAAWA,UAAS;AACpB,WAAO;AAAA,EACT;AACF;AAOA,SAAS,qBAAqB,SAASD,KAAI,KAAK;AAC9C,QAAM,OAAO;AAEb,SAAO;AAOP,WAAS,eAAe,MAAM;AAC5B,OAAO,mBAAmB,IAAI,GAAG,wBAAwB;AACzD,YAAQ,KAAK,MAAM,YAAY;AAC/B,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,aAAa,SAAS,UAAU,MAAM,UAAU;AAAA,EACzD;AAOA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,IAAI,IAAI;AAAA,IACjB;AAGA;AAAA,MACE,KAAK,OAAO,WAAW,QAAQ;AAAA,MAC/B;AAAA,IACF;AAEA,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAE/C,QACE,CAAC,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,KAC5D,QACA,KAAK,CAAC,EAAE,SAAS,MAAM,cACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,UAAU,UAAU,SAC1D;AACA,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,QAAQ,UAAU,KAAK,OAAO,WAAW,MAAM,KAAKA,GAAE,EAAE,IAAI;AAAA,EACrE;AACF;;;ACxIO,SAAS,mBACd,SACAE,KACA,KACA,MACA,aACA,mBACA,SACA,YACA,KACA;AACA,QAAM,QAAQ,OAAO,OAAO;AAC5B,MAAI,UAAU;AAEd,SAAO;AAcP,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,UAAU;AAC3B,cAAQ,MAAM,IAAI;AAClB,cAAQ,MAAM,WAAW;AACzB,cAAQ,MAAM,iBAAiB;AAC/B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,iBAAiB;AAC9B,aAAO;AAAA,IACT;AAGA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,oBACf,aAAa,IAAI,GACjB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,MAAM,IAAI;AAClB,YAAQ,MAAM,OAAO;AACrB,YAAQ,MAAM,UAAU;AACxB,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,eAAe,MAAM;AAC5B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,MAAM,iBAAiB;AAC/B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,iBAAiB;AAC9B,cAAQ,KAAK,WAAW;AACxB,cAAQ,KAAK,IAAI;AACjB,aAAOA;AAAA,IACT;AAEA,YAAQ,MAAM,UAAU;AACxB,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,SAAS,IAAI;AAAA,EACtB;AAYA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,UAAU;AACvB,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,YACf,mBAAmB,IAAI,GACvB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO,SAAS,MAAM,YAAY,iBAAiB;AAAA,EACrD;AAYA,WAAS,eAAe,MAAM;AAC5B,QACE,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,WACf;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,SAAS,IAAI;AAAA,EACtB;AAYA,WAAS,IAAI,MAAM;AACjB,QACE,CAAC,YACA,SAAS,MAAM,OACd,SAAS,MAAM,oBACf,0BAA0B,IAAI,IAChC;AACA,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,UAAU;AACvB,cAAQ,KAAK,OAAO;AACpB,cAAQ,KAAK,IAAI;AACjB,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,QAAI,UAAU,SAAS,SAAS,MAAM,iBAAiB;AACrD,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,kBAAkB;AACnC,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAKA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,mBACf,aAAa,IAAI,GACjB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO,SAAS,MAAM,YAAY,YAAY;AAAA,EAChD;AAYA,WAAS,UAAU,MAAM;AACvB,QACE,SAAS,MAAM,mBACf,SAAS,MAAM,oBACf,SAAS,MAAM,WACf;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AClNO,SAAS,aAAa,SAASC,KAAI,KAAK,MAAM,YAAY,YAAY;AAC3E,QAAM,OAAO;AACb,MAAI,OAAO;AAEX,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,mBAAmB,cAAc;AACvD,YAAQ,MAAM,IAAI;AAClB,YAAQ,MAAM,UAAU;AACxB,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,UAAU;AACvB,YAAQ,MAAM,UAAU;AACxB,WAAO;AAAA,EACT;AAYA,WAAS,QAAQ,MAAM;AACrB,QACE,OAAO,UAAU,wBACjB,SAAS,MAAM,OACf,SAAS,MAAM,qBACd,SAAS,MAAM,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,IAMtC,SAAS,MAAM,SACd,CAAC,QACD,4BAA4B,KAAK,OAAO,YAC1C;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,KAAK,UAAU;AACvB,cAAQ,MAAM,UAAU;AACxB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,UAAU;AACvB,cAAQ,KAAK,IAAI;AACjB,aAAOA;AAAA,IACT;AAGA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,YAAY,IAAI;AAAA,EACzB;AAYA,WAAS,YAAY,MAAM;AACzB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,qBACf,SAAS,MAAM,sBACf,mBAAmB,IAAI,KACvB,SAAS,UAAU,sBACnB;AACA,cAAQ,KAAK,MAAM,WAAW;AAC9B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,QAAI,CAAC,KAAM,QAAO,CAAC,cAAc,IAAI;AACrC,WAAO,SAAS,MAAM,YAAY,cAAc;AAAA,EAClD;AAYA,WAAS,YAAY,MAAM;AACzB,QACE,SAAS,MAAM,qBACf,SAAS,MAAM,aACf,SAAS,MAAM,oBACf;AACA,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AACF;;;AC/HO,SAAS,aAAa,SAASC,KAAI,KAAK,MAAM,YAAY,YAAY;AAE3E,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,QACE,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,iBACf;AACA,cAAQ,MAAM,IAAI;AAClB,cAAQ,MAAM,UAAU;AACxB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,UAAU;AACvB,eAAS,SAAS,MAAM,kBAAkB,MAAM,mBAAmB;AACnE,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAcA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,QAAQ;AACnB,cAAQ,MAAM,UAAU;AACxB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,UAAU;AACvB,cAAQ,KAAK,IAAI;AACjB,aAAOA;AAAA,IACT;AAEA,YAAQ,MAAM,UAAU;AACxB,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,QAAQ;AACnB,cAAQ,KAAK,UAAU;AACvB,aAAO,MAAM,MAAM;AAAA,IACrB;AAEA,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAGA,QAAI,mBAAmB,IAAI,GAAG;AAE5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO,aAAa,SAAS,SAAS,MAAM,UAAU;AAAA,IACxD;AAEA,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,OAAO,IAAI;AAAA,EACpB;AAOA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,UAAU,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AACrE,cAAQ,KAAK,MAAM,WAAW;AAC9B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO,SAAS,MAAM,YAAY,SAAS;AAAA,EAC7C;AAYA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,UAAU,SAAS,MAAM,WAAW;AAC/C,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,OAAO,IAAI;AAAA,EACpB;AACF;;;AC9IO,SAAS,kBAAkB,SAASC,KAAI;AAE7C,MAAI;AAEJ,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AACP,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,OAAO,MAAM,aAAa,MAAM;AAAA,MAClC,EAAE,IAAI;AAAA,IACR;AAEA,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;;;AC5BO,IAAM,aAAa,EAAC,MAAM,cAAc,UAAU,mBAAkB;AAG3E,IAAM,cAAc,EAAC,SAAS,MAAM,UAAU,oBAAmB;AAOjE,SAAS,mBAAmB,SAASC,KAAI,KAAK;AAC5C,QAAM,OAAO;AAEb,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AAInB,YAAQ,MAAM,MAAM,UAAU;AAC9B,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AAEpB,OAAO,SAAS,MAAM,mBAAmB,cAAc;AACvD,WAAO,aAAa;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,WAAW,MAAM;AACxB,iBAAa;AAAA,MACX,KAAK,eAAe,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,IACzE;AAEA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,MAAM,MAAM,gBAAgB;AACpC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,gBAAgB;AACnC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,YAAY,MAAM;AAEzB,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,iBAAiB,EAAE,IAAI,IAClD,kBAAkB,IAAI;AAAA,EAC5B;AAYA,WAAS,kBAAkB,MAAM;AAC/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,QAAQ,QAAQ,aAAa,OAAO,KAAK,EAAE,IAAI;AAAA,EACxD;AAcA,WAAS,MAAM,MAAM;AACnB,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,iBAAiB,MAAM,UAAU,EAAE,IAAI,IAC7D,gBAAgB,IAAI;AAAA,EAC1B;AAcA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,UAAU;AAK7B,WAAK,OAAO,QAAQ,KAAK,UAAU;AAKnC,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,oBAAoB,SAASA,KAAI,KAAK;AAC7C,SAAOC;AAcP,WAASA,aAAY,MAAM;AACzB,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,YAAY,EAAE,IAAI,IAC7C,IAAI,IAAI;AAAA,EACd;AAaA,WAAS,aAAa,MAAM;AAC1B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,WAAW,MAAM;AACxB,WAAO,cAAc,IAAI,IACrB;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,IACR,EAAE,IAAI,IACN,6BAA6B,IAAI;AAAA,EACvC;AAYA,WAAS,6BAA6B,MAAM;AAC1C,WAAO,SAAS,MAAM,OAAO,mBAAmB,IAAI,IAAID,IAAG,IAAI,IAAI,IAAI,IAAI;AAAA,EAC7E;AACF;;;ACxRO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASE,KAAI,KAAK;AACjD,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,WAAW,eAAe;AAChD,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,MAAM,MAAM;AACnB,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,KAAK,MAAM,eAAe;AAClC,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AC1CO,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,UAAU;AACZ;AAGA,SAAS,kBAAkB,QAAQ,SAAS;AAC1C,MAAI,aAAa,OAAO,SAAS;AACjC,MAAI,eAAe;AAEnB,MAAIC;AAEJ,MAAI;AAGJ,MAAI,OAAO,YAAY,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY;AACrD,oBAAgB;AAAA,EAClB;AAGA,MACE,aAAa,IAAI,gBACjB,OAAO,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,YACrC;AACA,kBAAc;AAAA,EAChB;AAEA,MACE,OAAO,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,uBACpC,iBAAiB,aAAa,KAC5B,aAAa,IAAI,gBAChB,OAAO,aAAa,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,aAC7C;AACA,kBAAc,eAAe,MAAM,aAAa,IAAI;AAAA,EACtD;AAEA,MAAI,aAAa,cAAc;AAC7B,IAAAA,WAAU;AAAA,MACR,MAAM,MAAM;AAAA,MACZ,OAAO,OAAO,YAAY,EAAE,CAAC,EAAE;AAAA,MAC/B,KAAK,OAAO,UAAU,EAAE,CAAC,EAAE;AAAA,IAC7B;AACA,WAAO;AAAA,MACL,MAAM,MAAM;AAAA,MACZ,OAAO,OAAO,YAAY,EAAE,CAAC,EAAE;AAAA,MAC/B,KAAK,OAAO,UAAU,EAAE,CAAC,EAAE;AAAA,MAC3B,aAAa,UAAU;AAAA,IACzB;AAEA,WAAO,QAAQ,cAAc,aAAa,eAAe,GAAG;AAAA,MAC1D,CAAC,SAASA,UAAS,OAAO;AAAA,MAC1B,CAAC,SAAS,MAAM,OAAO;AAAA,MACvB,CAAC,QAAQ,MAAM,OAAO;AAAA,MACtB,CAAC,QAAQA,UAAS,OAAO;AAAA,IAC3B,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAOA,SAAS,mBAAmB,SAASC,KAAI,KAAK;AAC5C,MAAI,OAAO;AAEX,SAAO;AAYP,WAAS,MAAM,MAAM;AAEnB,YAAQ,MAAM,MAAM,UAAU;AAC9B,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,OAAO,SAAS,MAAM,YAAY,cAAc;AAChD,YAAQ,MAAM,MAAM,kBAAkB;AACtC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,aAAa,MAAM;AAC1B,QACE,SAAS,MAAM,cACf,SAAS,UAAU,+BACnB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,OAAO,0BAA0B,IAAI,GAAG;AACzD,cAAQ,KAAK,MAAM,kBAAkB;AACrC,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,YAAY;AAC7B,cAAQ,MAAM,MAAM,kBAAkB;AACtC,aAAO,gBAAgB,IAAI;AAAA,IAC7B;AAEA,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,UAAU;AAI7B,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,aAAO,aAAa,SAAS,SAAS,MAAM,UAAU,EAAE,IAAI;AAAA,IAC9D;AAIA,YAAQ,MAAM,MAAM,cAAc;AAClC,WAAO,KAAK,IAAI;AAAA,EAClB;AAcA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,YAAY;AAC7B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,kBAAkB;AACrC,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,KAAK,MAAM;AAClB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,cACf,0BAA0B,IAAI,GAC9B;AACA,cAAQ,KAAK,MAAM,cAAc;AACjC,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AACF;;;ACzNO,IAAM,iBAAiB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAcO,IAAM,eAAe,CAAC,OAAO,UAAU,SAAS,UAAU;;;ACpE1D,IAAM,WAAW;AAAA,EACtB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,WAAW;AAAA,EACX,UAAU;AACZ;AAGA,IAAM,kBAAkB,EAAC,SAAS,MAAM,UAAU,wBAAuB;AACzE,IAAM,2BAA2B;AAAA,EAC/B,SAAS;AAAA,EACT,UAAU;AACZ;AAGA,SAAS,kBAAkB,QAAQ;AACjC,MAAI,QAAQ,OAAO;AAEnB,SAAO,SAAS;AACd,QACE,OAAO,KAAK,EAAE,CAAC,MAAM,WACrB,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,UAChC;AACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,KAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY;AAE/D,WAAO,KAAK,EAAE,CAAC,EAAE,QAAQ,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE;AAE9C,WAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE;AAElD,WAAO,OAAO,QAAQ,GAAG,CAAC;AAAA,EAC5B;AAEA,SAAO;AACT;AAOA,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AAEb,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AAEnB,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,OAAO,SAAS,MAAM,UAAU,cAAc;AAC9C,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,YAAY;AAChC,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAgBA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,iBAAiB;AAClC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,mBAAa;AACb,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,cAAc;AAC/B,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AAMnB,aAAO,KAAK,YAAYA,MAAK;AAAA,IAC/B;AAGA,QAAI,WAAW,IAAI,GAAG;AACpB,SAAO,SAAS,IAAI;AACpB,cAAQ,QAAQ,IAAI;AACpB,eAAS,OAAO,aAAa,IAAI;AACjC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAgBA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AACnB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,mBAAmB;AACpC,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AACnB,cAAQ;AACR,aAAO;AAAA,IACT;AAGA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AAGnB,aAAO,KAAK,YAAYA,MAAK;AAAA,IAC/B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,kBAAkB,MAAM;AAC/B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AAGpB,aAAO,KAAK,YAAYA,MAAK;AAAA,IAC/B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,gBAAgB,MAAM;AAC7B,UAAM,QAAQ,UAAU;AAExB,QAAI,SAAS,MAAM,WAAW,OAAO,GAAG;AACtC,cAAQ,QAAQ,IAAI;AAEpB,UAAI,UAAU,MAAM,QAAQ;AAG1B,eAAO,KAAK,YAAYA,MAAK;AAAA,MAC/B;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,cAAc,MAAM;AAC3B,QAAI,WAAW,IAAI,GAAG;AACpB,SAAO,SAAS,IAAI;AACpB,cAAQ,QAAQ,IAAI;AACpB,eAAS,OAAO,aAAa,IAAI;AACjC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAcA,WAAS,QAAQ,MAAM;AACrB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,YAAM,QAAQ,SAAS,MAAM;AAC7B,YAAM,OAAO,OAAO,YAAY;AAEhC,UAAI,CAAC,SAAS,CAAC,cAAc,aAAa,SAAS,IAAI,GAAG;AACxD,iBAAS,UAAU;AAGnB,eAAO,KAAK,YAAYA,IAAG,IAAI,IAAI,aAAa,IAAI;AAAA,MACtD;AAEA,UAAI,eAAe,SAAS,OAAO,YAAY,CAAC,GAAG;AACjD,iBAAS,UAAU;AAEnB,YAAI,OAAO;AACT,kBAAQ,QAAQ,IAAI;AACpB,iBAAO;AAAA,QACT;AAIA,eAAO,KAAK,YAAYA,IAAG,IAAI,IAAI,aAAa,IAAI;AAAA,MACtD;AAEA,eAAS,UAAU;AAEnB,aAAO,KAAK,aAAa,CAAC,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IACtD,IAAI,IAAI,IACR,aACE,wBAAwB,IAAI,IAC5B,4BAA4B,IAAI;AAAA,IACxC;AAGA,QAAI,SAAS,MAAM,QAAQ,kBAAkB,IAAI,GAAG;AAClD,cAAQ,QAAQ,IAAI;AACpB,gBAAU,OAAO,aAAa,IAAI;AAClC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,iBAAiB,MAAM;AAC9B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AAGpB,aAAO,KAAK,YAAYA,MAAK;AAAA,IAC/B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,wBAAwB,MAAM;AACrC,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AAyBA,WAAS,4BAA4B,MAAM;AACzC,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,SAAS,SAAS,MAAM,cAAc,WAAW,IAAI,GAAG;AACzE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AAgBA,WAAS,sBAAsB,MAAM;AAEnC,QACE,SAAS,MAAM,QACf,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,cACf,kBAAkB,IAAI,GACtB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,2BAA2B,IAAI;AAAA,EACxC;AAeA,WAAS,2BAA2B,MAAM;AACxC,QAAI,SAAS,MAAM,UAAU;AAC3B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,4BAA4B,IAAI;AAAA,EACzC;AAeA,WAAS,6BAA6B,MAAM;AAC1C,QACE,SAAS,MAAM,OACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,aACf;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,iBAAiB,SAAS,MAAM,YAAY;AAC7D,cAAQ,QAAQ,IAAI;AACpB,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,+BAA+B,IAAI;AAAA,EAC5C;AAcA,WAAS,6BAA6B,MAAM;AAC1C,QAAI,SAAS,SAAS;AACpB,cAAQ,QAAQ,IAAI;AACpB,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,+BAA+B,MAAM;AAC5C,QACE,SAAS,MAAM,OACf,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,SACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,2BAA2B,IAAI;AAAA,IACxC;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,kCAAkC,MAAM;AAC/C,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,cAAc,IAAI,GAClB;AACA,aAAO,4BAA4B,IAAI;AAAA,IACzC;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,cAAc,MAAM;AAC3B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAGlD,aAAO,aAAa,IAAI;AAAA,IAC1B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,QAAQ,WAAW,UAAU,aAAa;AAC3D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,YAAY,WAAW,UAAU,SAAS;AAC3D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,eAAe,WAAW,UAAU,iBAAiB;AACtE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,gBAAgB,WAAW,UAAU,iBAAiB;AACvE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,sBAAsB,WAAW,UAAU,WAAW;AACvE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QACE,mBAAmB,IAAI,MACtB,WAAW,UAAU,aAAa,WAAW,UAAU,eACxD;AACA,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,IAAI;AAAA,IACR;AAEA,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,kBAAkB,MAAM;AAC/B,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAaA,WAAS,yBAAyB,MAAM;AACtC,OAAO,mBAAmB,IAAI,CAAC;AAC/B,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAaA,WAAS,mBAAmB,MAAM;AAChC,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAEA,YAAQ,MAAM,MAAM,YAAY;AAChC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,0BAA0B,MAAM;AACvC,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,uBAAuB,MAAM;AACpC,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,eAAS;AACT,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,sBAAsB,MAAM;AACnC,QAAI,SAAS,MAAM,aAAa;AAC9B,YAAM,OAAO,OAAO,YAAY;AAEhC,UAAI,aAAa,SAAS,IAAI,GAAG;AAC/B,gBAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,aAAO,aAAa,IAAI;AAAA,IAC1B;AAEA,QAAI,WAAW,IAAI,KAAK,OAAO,SAAS,UAAU,gBAAgB;AAChE,SAAO,SAAS,IAAI;AACpB,cAAQ,QAAQ,IAAI;AACpB,gBAAU,OAAO,aAAa,IAAI;AAClC,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,wBAAwB,MAAM;AACrC,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAoBA,WAAS,8BAA8B,MAAM;AAC3C,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,QAAQ,WAAW,UAAU,aAAa;AAC3D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,kBAAkB,MAAM;AAC/B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,kBAAkB,MAAM;AAC/B,YAAQ,KAAK,MAAM,QAAQ;AAK3B,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAOA,SAAS,iCAAiC,SAASA,KAAI,KAAK;AAC1D,QAAM,OAAO;AAEb,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAaA,WAAS,MAAM,MAAM;AACnB,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAIA,IAAG,IAAI;AAAA,EAChE;AACF;AAOA,SAAS,wBAAwB,SAASA,KAAI,KAAK;AACjD,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,OAAO,mBAAmB,IAAI,GAAG,wBAAwB;AACzD,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,QAAQ,QAAQ,WAAWA,KAAI,GAAG;AAAA,EAC3C;AACF;;;ACt8BO,IAAM,WAAW,EAAC,MAAM,YAAY,UAAU,iBAAgB;AAOrE,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AAEb,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,UAAU,cAAc;AAC9C,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,YAAY;AAChC,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAgBA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,iBAAiB;AAClC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,cAAc;AAC/B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAgBA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,mBAAmB;AACpC,cAAQ,QAAQ,IAAI;AACpB,cAAQ;AACR,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,kBAAkB,MAAM;AAC/B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,WAAW,MAAM;AACxB,WAAO,SAAS,MAAM,cAClB,IAAI,IAAI,IACR,SAAS,MAAM,OACb,aAAa,IAAI,IACjB,QAAQ,IAAI;AAAA,EACpB;AAYA,WAAS,gBAAgB,MAAM;AAC7B,UAAM,QAAQ,UAAU;AAExB,QAAI,SAAS,MAAM,WAAW,OAAO,GAAG;AACtC,cAAQ,QAAQ,IAAI;AACpB,aAAO,UAAU,MAAM,SAAS,QAAQ;AAAA,IAC1C;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,IAAI;AAAA,EACnB;AAYA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,MAAM,aAAa;AAC9B,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,IAAI;AAAA,EACnB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,OAAO,SAAS,MAAM,aAAa;AACpD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,cAAc;AAC/B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,SAAS,MAAM,cAAc,IAAI,IAAI,IAAI,YAAY,IAAI;AAAA,EAClE;AAYA,WAAS,cAAc,MAAM;AAE3B,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,SAAS,MAAM;AAEtB,QAAI,SAAS,MAAM,QAAQ,kBAAkB,IAAI,GAAG;AAClD,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,gBAAgB,IAAI;AAAA,EAC7B;AAYA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,QAAQ,MAAM;AAErB,QAAI,SAAS,MAAM,QAAQ,kBAAkB,IAAI,GAAG;AAClD,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,eAAe,MAAM;AAC5B,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,SAAS,SAAS,MAAM,cAAc,WAAW,IAAI,GAAG;AACzE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,qBAAqB,MAAM;AAElC,QACE,SAAS,MAAM,QACf,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,cACf,kBAAkB,IAAI,GACtB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,0BAA0B,IAAI;AAAA,EACvC;AAaA,WAAS,0BAA0B,MAAM;AACvC,QAAI,SAAS,MAAM,UAAU;AAC3B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,eAAe,IAAI;AAAA,EAC5B;AAaA,WAAS,4BAA4B,MAAM;AACzC,QACE,SAAS,MAAM,OACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,aACf;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,iBAAiB,SAAS,MAAM,YAAY;AAC7D,cAAQ,QAAQ,IAAI;AACpB,eAAS;AACT,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,4BAA4B,MAAM;AACzC,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB,eAAS;AACT,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,8BAA8B,MAAM;AAC3C,QACE,SAAS,MAAM,OACf,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,aACf;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,iCAAiC,MAAM;AAC9C,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,IAAI,MAAM;AACjB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,YAAY;AAC/B,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAgBA,WAAS,iBAAiB,MAAM;AAC9B,OAAO,aAAa,uBAAuB;AAC3C,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,KAAK,MAAM,YAAY;AAC/B,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAgBA,WAAS,gBAAgB,MAAM;AAE7B;AAAA,MACE,KAAK,OAAO,WAAW,QAAQ;AAAA,MAC/B;AAAA,IACF;AACA,WAAO,cAAc,IAAI,IACrB;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,IAChB,EAAE,IAAI,IACN,sBAAsB,IAAI;AAAA,EAChC;AAgBA,WAAS,sBAAsB,MAAM;AACnC,YAAQ,MAAM,MAAM,YAAY;AAChC,WAAO,YAAY,IAAI;AAAA,EACzB;AACF;;;ACtvBO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AACZ;AAGA,IAAM,oBAAoB,EAAC,UAAU,iBAAgB;AAErD,IAAM,yBAAyB,EAAC,UAAU,sBAAqB;AAE/D,IAAM,8BAA8B,EAAC,UAAU,2BAA0B;AAGzE,SAAS,mBAAmB,QAAQ;AAClC,MAAI,QAAQ;AAEZ,QAAM,YAAY,CAAC;AACnB,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAM,QAAQ,OAAO,KAAK,EAAE,CAAC;AAC7B,cAAU,KAAK,OAAO,KAAK,CAAC;AAE5B,QACE,MAAM,SAAS,MAAM,cACrB,MAAM,SAAS,MAAM,aACrB,MAAM,SAAS,MAAM,UACrB;AAEA,YAAM,SAAS,MAAM,SAAS,MAAM,aAAa,IAAI;AACrD,YAAM,OAAO,MAAM;AACnB,eAAS;AAAA,IACX;AAAA,EACF;AAGA,MAAI,OAAO,WAAW,UAAU,QAAQ;AACtC,WAAO,QAAQ,GAAG,OAAO,QAAQ,SAAS;AAAA,EAC5C;AAEA,SAAO;AACT;AAGA,SAAS,kBAAkB,QAAQ,SAAS;AAC1C,MAAI,QAAQ,OAAO;AACnB,MAAI,SAAS;AAEb,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAGJ,SAAO,SAAS;AACd,YAAQ,OAAO,KAAK,EAAE,CAAC;AAEvB,QAAI,MAAM;AAER,UACE,MAAM,SAAS,MAAM,QACpB,MAAM,SAAS,MAAM,aAAa,MAAM,WACzC;AACA;AAAA,MACF;AAIA,UAAI,OAAO,KAAK,EAAE,CAAC,MAAM,WAAW,MAAM,SAAS,MAAM,WAAW;AAClE,cAAM,YAAY;AAAA,MACpB;AAAA,IACF,WAAW,OAAO;AAChB,UACE,OAAO,KAAK,EAAE,CAAC,MAAM,YACpB,MAAM,SAAS,MAAM,cAAc,MAAM,SAAS,MAAM,cACzD,CAAC,MAAM,WACP;AACA,eAAO;AAEP,YAAI,MAAM,SAAS,MAAM,WAAW;AAClC,mBAAS;AACT;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,MAAM,SAAS,MAAM,UAAU;AACxC,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,KAAO,SAAS,QAAW,gCAAgC;AAC3D,KAAO,UAAU,QAAW,iCAAiC;AAE7D,QAAM,QAAQ;AAAA,IACZ,MAAM,OAAO,IAAI,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY,MAAM,OAAO,MAAM;AAAA,IACpE,OAAO,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,MAAK;AAAA,IAChC,KAAK,EAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,IAAG;AAAA,EAC3C;AAEA,QAAM,QAAQ;AAAA,IACZ,MAAM,MAAM;AAAA,IACZ,OAAO,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,MAAK;AAAA,IAChC,KAAK,EAAC,GAAG,OAAO,KAAK,EAAE,CAAC,EAAE,IAAG;AAAA,EAC/B;AAEA,QAAM,OAAO;AAAA,IACX,MAAM,MAAM;AAAA,IACZ,OAAO,EAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,IAAG;AAAA,IAC3C,KAAK,EAAC,GAAG,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAK;AAAA,EACrC;AAEA,UAAQ;AAAA,IACN,CAAC,SAAS,OAAO,OAAO;AAAA,IACxB,CAAC,SAAS,OAAO,OAAO;AAAA,EAC1B;AAGA,UAAQ,KAAK,OAAO,OAAO,MAAM,OAAO,GAAG,OAAO,SAAS,CAAC,CAAC;AAG7D,UAAQ,KAAK,OAAO,CAAC,CAAC,SAAS,MAAM,OAAO,CAAC,CAAC;AAG9C;AAAA,IACE,QAAQ,OAAO,WAAW,WAAW;AAAA,IACrC;AAAA,EACF;AAEA,UAAQ;AAAA,IACN;AAAA,IACA;AAAA,MACE,QAAQ,OAAO,WAAW,WAAW;AAAA,MACrC,OAAO,MAAM,OAAO,SAAS,GAAG,QAAQ,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAGA,UAAQ,KAAK,OAAO;AAAA,IAClB,CAAC,QAAQ,MAAM,OAAO;AAAA,IACtB,OAAO,QAAQ,CAAC;AAAA,IAChB,OAAO,QAAQ,CAAC;AAAA,IAChB,CAAC,QAAQ,OAAO,OAAO;AAAA,EACzB,CAAC;AAGD,UAAQ,KAAK,OAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAG3C,UAAQ,KAAK,OAAO,CAAC,CAAC,QAAQ,OAAO,OAAO,CAAC,CAAC;AAE9C,SAAO,QAAQ,MAAM,OAAO,QAAQ,KAAK;AAEzC,SAAO;AACT;AAOA,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AACb,MAAI,QAAQ,KAAK,OAAO;AAExB,MAAI;AAEJ,MAAI;AAGJ,SAAO,SAAS;AACd,SACG,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACpC,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACvC,CAAC,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,WACvB;AACA,mBAAa,KAAK,OAAO,KAAK,EAAE,CAAC;AACjC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAiBP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,oBAAoB,cAAc;AAGxD,QAAI,CAAC,YAAY;AACf,aAAO,IAAI,IAAI;AAAA,IACjB;AAWA,QAAI,WAAW,WAAW;AACxB,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,cAAU,KAAK,OAAO,QAAQ;AAAA,MAC5B;AAAA,QACE,KAAK,eAAe,EAAC,OAAO,WAAW,KAAK,KAAK,KAAK,IAAI,EAAC,CAAC;AAAA,MAC9D;AAAA,IACF;AACA,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,WAAW;AAC/B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,WAAW;AAC9B,YAAQ,KAAK,MAAM,QAAQ;AAC3B,WAAO;AAAA,EACT;AAkBA,WAAS,MAAM,MAAM;AAKnB,QAAI,SAAS,MAAM,iBAAiB;AAClC,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA,UAAU,aAAa;AAAA,MACzB,EAAE,IAAI;AAAA,IACR;AAGA,QAAI,SAAS,MAAM,mBAAmB;AACpC,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA,UAAU,mBAAmB;AAAA,MAC/B,EAAE,IAAI;AAAA,IACR;AAGA,WAAO,UAAU,WAAW,IAAI,IAAI,YAAY,IAAI;AAAA,EACtD;AAgBA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAkBA,WAAS,WAAW,MAAM;AAExB,WAAOA,IAAG,IAAI;AAAA,EAChB;AAkBA,WAAS,YAAY,MAAM;AACzB,eAAW,YAAY;AACvB,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,iBAAiB,SAASA,KAAI,KAAK;AAC1C,SAAO;AAYP,WAAS,cAAc,MAAM;AAC3B,OAAO,SAAS,MAAM,iBAAiB,qBAAqB;AAC5D,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,cAAc;AAClC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,cAAc;AACjC,WAAO;AAAA,EACT;AAYA,WAAS,eAAe,MAAM;AAC5B,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,YAAY,EAAE,IAAI,IAC7C,aAAa,IAAI;AAAA,EACvB;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,kBAAkB;AACnC,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,yBAAyB,MAAM;AACtC,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,eAAe,EAAE,IAAI,IAChD,YAAY,IAAI;AAAA,EACtB;AAYA,WAAS,2BAA2B,MAAM;AACxC,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,gBAAgB,MAAM;AAC7B,QACE,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,iBACf;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACR,EAAE,IAAI;AAAA,IACR;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AAYA,WAAS,mBAAmB,MAAM;AAChC,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,WAAW,EAAE,IAAI,IAC5C,YAAY,IAAI;AAAA,EACtB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,kBAAkB;AACnC,cAAQ,MAAM,MAAM,cAAc;AAClC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,cAAc;AACjC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,sBAAsB,SAASA,KAAI,KAAK;AAC/C,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,cAAc,MAAM;AAC3B,OAAO,SAAS,MAAM,mBAAmB,uBAAuB;AAChE,WAAO,aAAa;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,mBAAmB,MAAM;AAChC,WAAO,KAAK,OAAO,QAAQ;AAAA,MACzB;AAAA,QACE,KAAK,eAAe,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,MACzE;AAAA,IACF,IACIA,IAAG,IAAI,IACP,IAAI,IAAI;AAAA,EACd;AAYA,WAAS,qBAAqB,MAAM;AAClC,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,2BAA2B,SAASA,KAAI,KAAK;AACpD,SAAO;AAcP,WAAS,wBAAwB,MAAM;AAErC,OAAO,SAAS,MAAM,mBAAmB,uBAAuB;AAChE,YAAQ,MAAM,MAAM,SAAS;AAC7B,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,eAAe;AAClC,WAAO;AAAA,EACT;AAcA,WAAS,uBAAuB,MAAM;AACpC,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,MAAM,MAAM,eAAe;AACnC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,eAAe;AAClC,cAAQ,KAAK,MAAM,SAAS;AAC5B,aAAOA;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;ACjoBO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,YAAY,SAAS;AAAA,EACrB,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASC,KAAI,KAAK;AACjD,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,iBAAiB,cAAc;AACrD,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,MAAM,MAAM,gBAAgB;AACpC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,gBAAgB;AACnC,WAAO;AAAA,EACT;AAYA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,mBAAmB;AACpC,cAAQ,MAAM,MAAM,WAAW;AAC/B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AA6BA,WAAS,MAAM,MAAM;AAMnB,WAAO,SAAS,MAAM,SACpB,4BAA4B,KAAK,OAAO,aACtC,IAAI,IAAI,IACRA,IAAG,IAAI;AAAA,EACb;AACF;;;AC/FO,IAAM,iBAAiB;AAAA,EAC5B,MAAM;AAAA,EACN,YAAY,SAAS;AAAA,EACrB,UAAU;AACZ;AAOA,SAAS,uBAAuB,SAASC,KAAI,KAAK;AAChD,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,mBAAmB,cAAc;AACvD,YAAQ,MAAM,MAAM,SAAS;AAC7B,YAAQ,MAAM,MAAM,WAAW;AAC/B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,WAAW;AAC9B,YAAQ,KAAK,MAAM,SAAS;AAC5B,WAAO;AAAA,EACT;AAGA,WAAS,MAAM,MAAM;AAKnB,WAAO,SAAS,MAAM,SACpB,4BAA4B,KAAK,OAAO,aACtC,IAAI,IAAI,IACRA,IAAG,IAAI;AAAA,EACb;AACF;;;AC9CO,IAAM,aAAa,EAAC,MAAM,cAAc,UAAU,mBAAkB;AAO3E,SAAS,mBAAmB,SAASC,KAAI;AACvC,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,aAAa,SAASA,KAAI,MAAM,UAAU;AAAA,EACnD;AACF;;;ACjBO,IAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,sBAAsB,SAASC,KAAI,KAAK;AAC/C,MAAI,OAAO;AAEX,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,YAAQ,MAAM,MAAM,aAAa;AAEjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB;AAAA,MACE,SAAS,MAAM,YACb,SAAS,MAAM,QACf,SAAS,MAAM;AAAA,MACjB;AAAA,IACF;AACA,aAAS;AACT,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,QAAQ;AACnB,cAAQ,MAAM,MAAM,qBAAqB;AACzC,aAAO,SAAS,IAAI;AAAA,IACtB;AAEA,QACE,QAAQ,UAAU,gCACjB,SAAS,MAAM,OAAO,mBAAmB,IAAI,IAC9C;AACA,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,qBAAqB;AACxC,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,SAAS,MAAM,UAAU,EAAE,IAAI,IACrD,QAAQ,IAAI;AAAA,EAClB;AACF;;;ACpGO,IAAM,OAAO;AAAA,EAClB,cAAc,EAAC,UAAU,yBAAwB;AAAA,EACjD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AACZ;AAGA,IAAM,oCAAoC;AAAA,EACxC,SAAS;AAAA,EACT,UAAU;AACZ;AAGA,IAAM,kBAAkB,EAAC,SAAS,MAAM,UAAU,eAAc;AAUhE,SAAS,kBAAkB,SAASC,KAAI,KAAK;AAC3C,QAAM,OAAO;AACb,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,MAAI,cACF,QAAQ,KAAK,CAAC,EAAE,SAAS,MAAM,aAC3B,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,SACtC;AACN,MAAI,OAAO;AAEX,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,UAAM,OACJ,KAAK,eAAe,SACnB,SAAS,MAAM,YAAY,SAAS,MAAM,YAAY,SAAS,MAAM,OAClE,MAAM,gBACN,MAAM;AAEZ,QACE,SAAS,MAAM,gBACX,CAAC,KAAK,eAAe,UAAU,SAAS,KAAK,eAAe,SAC5D,WAAW,IAAI,GACnB;AACA,UAAI,CAAC,KAAK,eAAe,MAAM;AAC7B,aAAK,eAAe,OAAO;AAC3B,gBAAQ,MAAM,MAAM,EAAC,YAAY,KAAI,CAAC;AAAA,MACxC;AAEA,UAAI,SAAS,MAAM,eAAe;AAChC,gBAAQ,MAAM,MAAM,cAAc;AAClC,eAAO,SAAS,MAAM,YAAY,SAAS,MAAM,OAC7C,QAAQ,MAAM,eAAe,KAAK,QAAQ,EAAE,IAAI,IAChD,SAAS,IAAI;AAAA,MACnB;AAEA,UAAI,CAAC,KAAK,aAAa,SAAS,MAAM,QAAQ;AAC5C,gBAAQ,MAAM,MAAM,cAAc;AAClC,gBAAQ,MAAM,MAAM,aAAa;AACjC,eAAO,OAAO,IAAI;AAAA,MACpB;AAAA,IACF;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAGA,WAAS,OAAO,MAAM;AACpB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,QAAI,WAAW,IAAI,KAAK,EAAE,OAAO,UAAU,sBAAsB;AAC/D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,SACG,CAAC,KAAK,aAAa,OAAO,OAC1B,KAAK,eAAe,SACjB,SAAS,KAAK,eAAe,SAC7B,SAAS,MAAM,oBAAoB,SAAS,MAAM,MACtD;AACA,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAO,SAAS,IAAI;AAAA,IACtB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAKA,WAAS,SAAS,MAAM;AACtB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,OAAO,SAAS,MAAM,KAAK,8BAA8B;AACzD,YAAQ,MAAM,MAAM,cAAc;AAClC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,cAAc;AACjC,SAAK,eAAe,SAAS,KAAK,eAAe,UAAU;AAC3D,WAAO,QAAQ;AAAA,MACb;AAAA;AAAA,MAEA,KAAK,YAAY,MAAM;AAAA,MACvB,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,WAAS,QAAQ,MAAM;AACrB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,SAAK,eAAe,mBAAmB;AACvC;AACA,WAAO,YAAY,IAAI;AAAA,EACzB;AAGA,WAAS,YAAY,MAAM;AACzB,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,MAAM,MAAM,wBAAwB;AAC5C,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,wBAAwB;AAC3C,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAGA,WAAS,YAAY,MAAM;AACzB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,SAAK,eAAe,OAClB,cACA,KAAK,eAAe,QAAQ,KAAK,MAAM,cAAc,GAAG,IAAI,EAAE;AAChE,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAOA,SAAS,yBAAyB,SAASA,KAAI,KAAK;AAClD,QAAM,OAAO;AAEb,KAAO,KAAK,gBAAgB,gBAAgB;AAC5C,OAAK,eAAe,aAAa;AAEjC,SAAO,QAAQ,MAAM,WAAW,SAAS,QAAQ;AAGjD,WAAS,QAAQ,MAAM;AACrB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,OAAO,OAAO,KAAK,eAAe,SAAS,UAAU,eAAe;AACpE,SAAK,eAAe,oBAClB,KAAK,eAAe,qBACpB,KAAK,eAAe;AAItB,WAAO;AAAA,MACL;AAAA,MACAA;AAAA,MACA,MAAM;AAAA,MACN,KAAK,eAAe,OAAO;AAAA,IAC7B,EAAE,IAAI;AAAA,EACR;AAGA,WAAS,SAAS,MAAM;AACtB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,QAAI,KAAK,eAAe,qBAAqB,CAAC,cAAc,IAAI,GAAG;AACjE,WAAK,eAAe,oBAAoB;AACxC,WAAK,eAAe,mBAAmB;AACvC,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,SAAK,eAAe,oBAAoB;AACxC,SAAK,eAAe,mBAAmB;AACvC,WAAO,QAAQ,QAAQ,iBAAiBA,KAAI,gBAAgB,EAAE,IAAI;AAAA,EACpE;AAGA,WAAS,iBAAiB,MAAM;AAC9B,OAAO,KAAK,gBAAgB,gBAAgB;AAE5C,SAAK,eAAe,aAAa;AAEjC,SAAK,YAAY;AAEjB;AAAA,MACE,KAAK,OAAO,WAAW,QAAQ;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,QAAQ,QAAQ,MAAMA,KAAI,GAAG;AAAA,MAC7B,MAAM;AAAA,MACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,IAChB,EAAE,IAAI;AAAA,EACR;AACF;AAOA,SAAS,eAAe,SAASA,KAAI,KAAK;AACxC,QAAM,OAAO;AAEb,KAAO,KAAK,gBAAgB,gBAAgB;AAC5C,KAAO,OAAO,KAAK,eAAe,SAAS,UAAU,eAAe;AAEpE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,KAAK,eAAe,OAAO;AAAA,EAC7B;AAGA,WAAS,YAAY,MAAM;AACzB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QACL,KAAK,CAAC,EAAE,SAAS,MAAM,kBACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,KAAK,eAAe,OACnEA,IAAG,IAAI,IACP,IAAI,IAAI;AAAA,EACd;AACF;AAOA,SAAS,gBAAgB,SAAS;AAChC,KAAO,KAAK,gBAAgB,gBAAgB;AAC5C,KAAO,OAAO,KAAK,eAAe,SAAS,UAAU,eAAe;AACpE,UAAQ,KAAK,KAAK,eAAe,IAAI;AACvC;AAOA,SAAS,iCAAiC,SAASA,KAAI,KAAK;AAC1D,QAAM,OAAO;AAGb;AAAA,IACE,KAAK,OAAO,WAAW,QAAQ;AAAA,IAC/B;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU,UAAU;AAAA,EAC1B;AAGA,WAAS,YAAY,MAAM;AACzB,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAE/C,WAAO,CAAC,cAAc,IAAI,KACxB,QACA,KAAK,CAAC,EAAE,SAAS,MAAM,2BACrBA,IAAG,IAAI,IACP,IAAI,IAAI;AAAA,EACd;AACF;;;AChSO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,WAAW;AAAA,EACX,UAAU;AACZ;AAGA,SAAS,yBAAyB,QAAQ,SAAS;AAEjD,MAAI,QAAQ,OAAO;AAEnB,MAAIC;AAEJ,MAAI;AAEJ,MAAIC;AAIJ,SAAO,SAAS;AACd,QAAI,OAAO,KAAK,EAAE,CAAC,MAAM,SAAS;AAChC,UAAI,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,SAAS;AAC3C,QAAAD,WAAU;AACV;AAAA,MACF;AAEA,UAAI,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,WAAW;AAC7C,eAAO;AAAA,MACT;AAAA,IACF,OAEK;AACH,UAAI,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,SAAS;AAE3C,eAAO,OAAO,OAAO,CAAC;AAAA,MACxB;AAEA,UAAI,CAACC,eAAc,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY;AAC7D,QAAAA,cAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,KAAO,SAAS,QAAW,qCAAqC;AAChE,KAAOD,aAAY,QAAW,qCAAqC;AACnE,KAAO,OAAOA,QAAO,EAAE,CAAC,MAAM,SAAS,8BAA8B;AACrE;AAAA,IACE,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM;AAAA,IACjC;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,MAAM,MAAM;AAAA,IACZ,OAAO,EAAC,GAAG,OAAOA,QAAO,EAAE,CAAC,EAAE,MAAK;AAAA,IACnC,KAAK,EAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,IAAG;AAAA,EAC3C;AAGA,SAAO,IAAI,EAAE,CAAC,EAAE,OAAO,MAAM;AAI7B,MAAIC,aAAY;AACd,WAAO,OAAO,MAAM,GAAG,CAAC,SAAS,SAAS,OAAO,CAAC;AAClD,WAAO,OAAOA,cAAa,GAAG,GAAG,CAAC,QAAQ,OAAOD,QAAO,EAAE,CAAC,GAAG,OAAO,CAAC;AACtE,WAAOA,QAAO,EAAE,CAAC,EAAE,MAAM,EAAC,GAAG,OAAOC,WAAU,EAAE,CAAC,EAAE,IAAG;AAAA,EACxD,OAAO;AACL,WAAOD,QAAO,EAAE,CAAC,IAAI;AAAA,EACvB;AAGA,SAAO,KAAK,CAAC,QAAQ,SAAS,OAAO,CAAC;AAEtC,SAAO;AACT;AAOA,SAAS,wBAAwB,SAASE,KAAI,KAAK;AACjD,QAAM,OAAO;AAEb,MAAI;AAEJ,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,QAAI,QAAQ,KAAK,OAAO;AAExB,QAAI;AAEJ;AAAA,MACE,SAAS,MAAM,QAAQ,SAAS,MAAM;AAAA,MACtC;AAAA,IACF;AAGA,WAAO,SAAS;AAGd,UACE,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACrC,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACrC,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,SACrC;AACA,oBAAY,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM;AACjD;AAAA,MACF;AAAA,IACF;AAIA,QAAI,CAAC,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,MAAM,KAAK,aAAa,YAAY;AACvE,cAAQ,MAAM,MAAM,iBAAiB;AACrC,eAAS;AACT,aAAO,OAAO,IAAI;AAAA,IACpB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAaA,WAAS,OAAO,MAAM;AACpB,YAAQ,MAAM,MAAM,yBAAyB;AAC7C,WAAO,OAAO,IAAI;AAAA,EACpB;AAaA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,yBAAyB;AAE5C,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,OAAO,MAAM,UAAU,EAAE,IAAI,IACnD,MAAM,IAAI;AAAA,EAChB;AAaA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,iBAAiB;AACpC,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AC7MA,IAAM,6BACJ;AAcK,SAAS,aAAa,OAAO;AAClC,SAAO,MAAM,QAAQ,4BAA4B,MAAM;AACzD;AAYA,SAAS,OAAO,IAAI,IAAI,IAAI;AAC1B,MAAI,IAAI;AAEN,WAAO;AAAA,EACT;AAGA,QAAM,OAAO,GAAG,WAAW,CAAC;AAE5B,MAAI,SAAS,MAAM,YAAY;AAC7B,UAAMC,QAAO,GAAG,WAAW,CAAC;AAC5B,UAAM,MAAMA,UAAS,MAAM,cAAcA,UAAS,MAAM;AACxD,WAAO;AAAA,MACL,GAAG,MAAM,MAAM,IAAI,CAAC;AAAA,MACpB,MAAM,UAAU,yBAAyB,UAAU;AAAA,IACrD;AAAA,EACF;AAEA,SAAO,8BAA8B,EAAE,KAAK;AAC9C;", "names": ["list", "all", "list", "values", "characterReference", "ok", "constructs", "ok", "ok", "previous", "ok", "ok", "ok", "ok", "ok", "effects", "nok", "start", "ok", "furtherStart", "ok", "list", "previous", "start", "ok", "previous", "ok", "ok", "ok", "ok", "ok", "titleBefore", "ok", "content", "ok", "ok", "ok", "ok", "ok", "ok", "ok", "ok", "ok", "content", "definition", "ok", "head"]}