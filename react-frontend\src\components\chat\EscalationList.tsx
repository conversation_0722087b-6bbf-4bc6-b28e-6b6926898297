import React, { useEffect, useState } from 'react';
import { ChevronLeft } from 'lucide-react';
import { escalationAPI } from '@/utils/api';

interface EscalationListProps {
  onBack: () => void;
}

interface FollowUp {
  message: string;
  date: string;
}

const EscalationList: React.FC<EscalationListProps> = ({ onBack }) => {
  const [escalations, setEscalations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIdx, setSelectedIdx] = useState<number | null>(null);
  const [followUps, setFollowUps] = useState<Record<number, FollowUp[]>>({});
  const [followUpMsg, setFollowUpMsg] = useState('');

  useEffect(() => {
    const fetchEscalations = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch('http://localhost:5052/api/escalations', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            ...(localStorage.getItem('token') ? { Authorization: `Bearer ${localStorage.getItem('token')}` } : {})
          }
        });
        if (!res.ok) throw new Error('Failed to fetch escalations');
        const data = await res.json();
        const escs = Array.isArray(data) ? data : (Array.isArray(data.escalations) ? data.escalations : []);
        setEscalations(escs);
        if (escs.length > 0) setSelectedIdx(0);
      } catch (err: any) {
        setError(err.message || 'Unknown error');
      } finally {
        setLoading(false);
      }
    };
    fetchEscalations();
  }, []);

  const handleSelect = (idx: number) => setSelectedIdx(idx);

  const handleFollowUpSend = () => {
    if (selectedIdx === null || !followUpMsg.trim()) return;
    setFollowUps(prev => {
      const id = escalations[selectedIdx].id;
      const arr = prev[id] || [];
      return {
        ...prev,
        [id]: [...arr, { message: followUpMsg, date: new Date().toISOString() }]
      };
    });
    setFollowUpMsg('');
  };

  return (
    <div className="flex h-[80vh] w-full bg-white rounded shadow overflow-hidden">
      {/* Sidebar */}
      <div className="w-[350px] min-w-[250px] max-w-[400px] border-r bg-gray-50 overflow-y-auto flex flex-col">
        <button onClick={onBack} className="m-3 p-2 rounded-full hover:bg-gray-200 w-8 h-8 flex items-center justify-center" title="Back">
          <ChevronLeft className="w-5 h-5" />
        </button>
        <h2 className="text-lg font-bold px-4 mb-2">My Escalations</h2>
        {loading ? (
          <div className="px-4">Loading...</div>
        ) : error ? (
          <div className="text-red-500 px-4">{error}</div>
        ) : escalations.length === 0 ? (
          <div className="px-4">No escalations found.</div>
        ) : (
          <ul className="flex-1">
            {escalations.map((esc, idx) => (
              <li
                key={esc.id || idx}
                className={`px-4 py-3 border-b cursor-pointer hover:bg-blue-50 ${selectedIdx === idx ? 'bg-blue-100 font-semibold border-l-4 border-blue-500' : ''}`}
                onClick={() => handleSelect(idx)}
              >
                <div className="truncate"><span className="font-bold">{esc.issue_type || 'N/A'}</span> <span className="text-xs text-gray-500 ml-2">{esc.created_at ? new Date(esc.created_at).toLocaleDateString() : ''}</span></div>
                <div className="truncate text-gray-600 text-sm">{esc.issue_description?.slice(0, 40) || ''}</div>
              </li>
            ))}
          </ul>
        )}
      </div>
      {/* Main Thread Panel */}
      <div className="flex-1 flex flex-col bg-gray-50">
        {selectedIdx !== null && escalations[selectedIdx] ? (
          <div className="p-6 flex-1 flex flex-col">
            {/* Escalation details header */}
            <div className="mb-4 p-4 rounded border bg-white shadow-sm">
              <div className="mb-2"><span className="font-bold">Subject:</span> {escalations[selectedIdx].issue_type || 'N/A'}</div>
              <div className="mb-2"><span className="font-bold">Status:</span> {escalations[selectedIdx].priority || 'N/A'}</div>
              <div className="mb-2"><span className="font-bold">Date:</span> {escalations[selectedIdx].created_at ? new Date(escalations[selectedIdx].created_at).toLocaleString() : 'N/A'}</div>
              <div className="mb-2"><span className="font-bold">Description:</span> {escalations[selectedIdx].issue_description || 'N/A'}</div>
            </div>
            {/* Thread bubbles */}
            <div className="flex-1 flex flex-col justify-end overflow-y-auto mb-4">
              <div className="space-y-4">
                {/* Follow-ups as bubbles */}
                {(followUps[escalations[selectedIdx].id] || []).map((f, i) => (
                  <div key={i} className="flex justify-end">
                    <div className="bg-green-100 border border-green-300 rounded-2xl px-4 py-3 max-w-2xl shadow-sm">
                      <div className="text-gray-800 whitespace-pre-line">{f.message}</div>
                      <div className="text-xs text-gray-500 mt-1 text-right">{new Date(f.date).toLocaleString()}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/* Follow-up input */}
            <div className="mt-auto flex gap-2">
              <input
                type="text"
                className="flex-1 border rounded px-3 py-2"
                placeholder="Type a follow-up message to HR..."
                value={followUpMsg}
                onChange={e => setFollowUpMsg(e.target.value)}
                onKeyDown={e => { if (e.key === 'Enter') handleFollowUpSend(); }}
              />
              <button
                className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
                onClick={handleFollowUpSend}
              >Send</button>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-400">Select an escalation to view details</div>
        )}
      </div>
    </div>
  );
};

export default EscalationList; 