import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getSelectedText, clearSelection, createQuestionFromSelection } from '@/utils/textSelection';

interface SelectionActionButtonProps {
  onAttachToInput: (selectedText: string) => void;
}

const SelectionActionButton: React.FC<SelectionActionButtonProps> = ({ onAttachToInput }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [selectedText, setSelectedText] = useState('');

  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = getSelectedText();
      
      if (selection && selection.text.length > 0) {
        setSelectedText(selection.text);
        
        // Calculate position for the button
        const range = selection.range;
        const rect = range.getBoundingClientRect();
        
        setPosition({
          x: rect.left + rect.width / 2,
          y: rect.top - 60 // Position above the selection
        });
        
        setIsVisible(true);
      } else {
        setIsVisible(false);
        setSelectedText('');
      }
    };

    const handleMouseUp = () => {
      // Small delay to ensure selection is complete
      setTimeout(handleSelectionChange, 10);
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        clearSelection();
        setIsVisible(false);
      }
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  const handleAttachToInput = () => {
    if (selectedText) {
      onAttachToInput(selectedText);
      clearSelection();
      setIsVisible(false);
    }
  };



    return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 10 }}
          transition={{ duration: 0.2 }}
          className="fixed z-50"
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`,
            transform: 'translateX(-50%)'
          }}
        >
                     <Button
             size="sm"
             onClick={handleAttachToInput}
             className="bg-gray-500 hover:bg-gray-600 text-white text-xs px-3 py-1 h-8 shadow-lg"
           >
             <MessageCircle className="w-3 h-3 mr-1" />
             Ask Proxima
           </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SelectionActionButton; 