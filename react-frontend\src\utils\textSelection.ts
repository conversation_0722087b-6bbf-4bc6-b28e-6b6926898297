export interface SelectedTextInfo {
  text: string;
  range: Range;
  element: HTMLElement;
}

export const getSelectedText = (): SelectedTextInfo | null => {
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) {
    return null;
  }

  const range = selection.getRangeAt(0);
  const text = selection.toString().trim();
  
  if (!text) {
    return null;
  }

  return {
    text,
    range,
    element: range.commonAncestorContainer.parentElement as HTMLElement
  };
};

export const createQuestionFromSelection = (selectedText: string): string => {
  // Create a question based on the selected text
  return `Can you explain more about: "${selectedText}"`;
};

export const clearSelection = (): void => {
  const selection = window.getSelection();
  if (selection) {
    selection.removeAllRanges();
  }
}; 