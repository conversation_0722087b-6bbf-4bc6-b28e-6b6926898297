"""
Payroll document database management using SQLite.
Handles storage and retrieval of payroll documents, extracted fields, and detected issues.
"""

import sqlite3
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from contextlib import contextmanager

from ..config import DATA_DIR
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Database path
PAYROLL_DB_PATH = DATA_DIR / "db" / "payroll.db"

# Ensure database directory exists
PAYROLL_DB_PATH.parent.mkdir(parents=True, exist_ok=True)


@dataclass
class PayrollDocument:
    """Represents a payroll document record."""
    id: Optional[int] = None
    employee_id: Optional[str] = None
    employee_name: Optional[str] = None
    organization: Optional[str] = None
    month: Optional[str] = None
    year: Optional[str] = None
    document_type: str = "payslip"
    file_path: Optional[str] = None
    file_hash: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    processing_status: str = "pending"  # pending, processed, failed
    confidence_score: float = 0.0


@dataclass
class ParsedField:
    """Represents an extracted field from a payroll document."""
    id: Optional[int] = None
    document_id: int = None
    field_name: str = None
    field_value: Optional[str] = None
    confidence: float = 0.0
    extraction_method: str = "ner"  # ner, regex, manual
    flagged: bool = False
    created_at: Optional[str] = None


@dataclass
class DetectedIssue:
    """Represents a detected issue in a payroll document."""
    id: Optional[int] = None
    document_id: int = None
    issue_type: str = None  # bonus_missing, tax_error, salary_delay, etc.
    description: Optional[str] = None
    confidence: float = 0.0
    status: str = "open"  # open, resolved, dismissed
    resolved_by: Optional[str] = None
    resolved_at: Optional[str] = None
    created_at: Optional[str] = None


class PayrollDatabase:
    """
    SQLite database manager for payroll documents and extracted data.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the payroll database.
        
        Args:
            db_path: Optional custom database path
        """
        self.db_path = db_path or str(PAYROLL_DB_PATH)
        self._init_database()
        logger.info(f"PayrollDatabase initialized at: {self.db_path}")
    
    def _init_database(self):
        """Initialize database tables if they don't exist."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Create documents table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id TEXT,
                    employee_name TEXT,
                    organization TEXT,
                    month TEXT,
                    year TEXT,
                    document_type TEXT DEFAULT 'payslip',
                    file_path TEXT,
                    file_hash TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processing_status TEXT DEFAULT 'pending',
                    confidence_score REAL DEFAULT 0.0
                )
            """)
            
            # Create parsed_fields table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS parsed_fields (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER NOT NULL,
                    field_name TEXT NOT NULL,
                    field_value TEXT,
                    confidence REAL DEFAULT 0.0,
                    extraction_method TEXT DEFAULT 'ner',
                    flagged BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
                )
            """)
            
            # Create detected_issues table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS detected_issues (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER NOT NULL,
                    issue_type TEXT NOT NULL,
                    description TEXT,
                    confidence REAL DEFAULT 0.0,
                    status TEXT DEFAULT 'open',
                    resolved_by TEXT,
                    resolved_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
                )
            """)
            
            # Create indexes for better query performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_documents_employee_id ON documents(employee_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_documents_month_year ON documents(month, year)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_documents_organization ON documents(organization)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_parsed_fields_document_id ON parsed_fields(document_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_parsed_fields_field_name ON parsed_fields(field_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_detected_issues_document_id ON detected_issues(document_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_detected_issues_type ON detected_issues(issue_type)")
            
            conn.commit()
            logger.info("Database tables initialized successfully")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper error handling."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def insert_document(self, document: PayrollDocument) -> int:
        """
        Insert a new payroll document record.
        
        Args:
            document: PayrollDocument instance
            
        Returns:
            Document ID of the inserted record
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO documents (
                    employee_id, employee_name, organization, month, year,
                    document_type, file_path, file_hash, processing_status, confidence_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                document.employee_id, document.employee_name, document.organization,
                document.month, document.year, document.document_type,
                document.file_path, document.file_hash, document.processing_status,
                document.confidence_score
            ))
            
            document_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Inserted document with ID: {document_id}")
            return document_id
    
    def insert_parsed_fields(self, document_id: int, fields: List[ParsedField]) -> List[int]:
        """
        Insert parsed fields for a document.
        
        Args:
            document_id: Document ID
            fields: List of ParsedField instances
            
        Returns:
            List of field IDs
        """
        field_ids = []
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            for field in fields:
                cursor.execute("""
                    INSERT INTO parsed_fields (
                        document_id, field_name, field_value, confidence,
                        extraction_method, flagged
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    document_id, field.field_name, field.field_value,
                    field.confidence, field.extraction_method, field.flagged
                ))
                
                field_ids.append(cursor.lastrowid)
            
            conn.commit()
            logger.info(f"Inserted {len(fields)} parsed fields for document {document_id}")
        
        return field_ids
    
    def insert_detected_issues(self, document_id: int, issues: List[DetectedIssue]) -> List[int]:
        """
        Insert detected issues for a document.
        
        Args:
            document_id: Document ID
            issues: List of DetectedIssue instances
            
        Returns:
            List of issue IDs
        """
        issue_ids = []
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            for issue in issues:
                cursor.execute("""
                    INSERT INTO detected_issues (
                        document_id, issue_type, description, confidence, status
                    ) VALUES (?, ?, ?, ?, ?)
                """, (
                    document_id, issue.issue_type, issue.description,
                    issue.confidence, issue.status
                ))
                
                issue_ids.append(cursor.lastrowid)
            
            conn.commit()
            logger.info(f"Inserted {len(issues)} detected issues for document {document_id}")
        
        return issue_ids

    def get_document_by_id(self, document_id: int) -> Optional[PayrollDocument]:
        """
        Get a document by its ID.

        Args:
            document_id: Document ID

        Returns:
            PayrollDocument instance or None if not found
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM documents WHERE id = ?", (document_id,))
            row = cursor.fetchone()

            if row:
                return PayrollDocument(**dict(row))
            return None

    def get_documents_by_employee(self, employee_id: str, limit: int = 50) -> List[PayrollDocument]:
        """
        Get documents for a specific employee.

        Args:
            employee_id: Employee ID
            limit: Maximum number of documents to return

        Returns:
            List of PayrollDocument instances
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM documents
                WHERE employee_id = ?
                ORDER BY year DESC, month DESC
                LIMIT ?
            """, (employee_id, limit))

            rows = cursor.fetchall()
            return [PayrollDocument(**dict(row)) for row in rows]

    def get_documents_by_month_year(self, month: str, year: str,
                                   organization: Optional[str] = None) -> List[PayrollDocument]:
        """
        Get documents for a specific month and year.

        Args:
            month: Month (MM format)
            year: Year (YYYY format)
            organization: Optional organization filter

        Returns:
            List of PayrollDocument instances
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()

            if organization:
                cursor.execute("""
                    SELECT * FROM documents
                    WHERE month = ? AND year = ? AND organization = ?
                    ORDER BY employee_id
                """, (month, year, organization))
            else:
                cursor.execute("""
                    SELECT * FROM documents
                    WHERE month = ? AND year = ?
                    ORDER BY organization, employee_id
                """, (month, year))

            rows = cursor.fetchall()
            return [PayrollDocument(**dict(row)) for row in rows]

    def get_parsed_fields(self, document_id: int) -> List[ParsedField]:
        """
        Get all parsed fields for a document.

        Args:
            document_id: Document ID

        Returns:
            List of ParsedField instances
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM parsed_fields
                WHERE document_id = ?
                ORDER BY field_name
            """, (document_id,))

            rows = cursor.fetchall()
            return [ParsedField(**dict(row)) for row in rows]

    def get_detected_issues(self, document_id: int, status: Optional[str] = None) -> List[DetectedIssue]:
        """
        Get detected issues for a document.

        Args:
            document_id: Document ID
            status: Optional status filter (open, resolved, dismissed)

        Returns:
            List of DetectedIssue instances
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()

            if status:
                cursor.execute("""
                    SELECT * FROM detected_issues
                    WHERE document_id = ? AND status = ?
                    ORDER BY confidence DESC, created_at DESC
                """, (document_id, status))
            else:
                cursor.execute("""
                    SELECT * FROM detected_issues
                    WHERE document_id = ?
                    ORDER BY confidence DESC, created_at DESC
                """, (document_id,))

            rows = cursor.fetchall()
            return [DetectedIssue(**dict(row)) for row in rows]

    def update_document_status(self, document_id: int, status: str, confidence_score: Optional[float] = None):
        """
        Update document processing status.

        Args:
            document_id: Document ID
            status: New status (pending, processed, failed)
            confidence_score: Optional confidence score update
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()

            if confidence_score is not None:
                cursor.execute("""
                    UPDATE documents
                    SET processing_status = ?, confidence_score = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (status, confidence_score, document_id))
            else:
                cursor.execute("""
                    UPDATE documents
                    SET processing_status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (status, document_id))

            conn.commit()
            logger.info(f"Updated document {document_id} status to: {status}")

    def resolve_issue(self, issue_id: int, resolved_by: str):
        """
        Mark an issue as resolved.

        Args:
            issue_id: Issue ID
            resolved_by: User who resolved the issue
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE detected_issues
                SET status = 'resolved', resolved_by = ?, resolved_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (resolved_by, issue_id))

            conn.commit()
            logger.info(f"Issue {issue_id} resolved by {resolved_by}")

    def get_document_with_details(self, document_id: int) -> Optional[Dict[str, Any]]:
        """
        Get complete document information including fields and issues.

        Args:
            document_id: Document ID

        Returns:
            Dictionary with document, fields, and issues data
        """
        document = self.get_document_by_id(document_id)
        if not document:
            return None

        fields = self.get_parsed_fields(document_id)
        issues = self.get_detected_issues(document_id)

        return {
            "document": asdict(document),
            "fields": [asdict(field) for field in fields],
            "issues": [asdict(issue) for issue in issues]
        }

    def search_documents(self, query: Dict[str, Any], limit: int = 100) -> List[PayrollDocument]:
        """
        Search documents with flexible criteria.

        Args:
            query: Dictionary with search criteria
            limit: Maximum number of results

        Returns:
            List of matching PayrollDocument instances
        """
        conditions = []
        params = []

        for key, value in query.items():
            if key in ['employee_id', 'employee_name', 'organization', 'month', 'year', 'document_type', 'processing_status']:
                if isinstance(value, str) and '%' in value:
                    conditions.append(f"{key} LIKE ?")
                else:
                    conditions.append(f"{key} = ?")
                params.append(value)

        if not conditions:
            # Return all documents if no criteria
            where_clause = ""
        else:
            where_clause = "WHERE " + " AND ".join(conditions)

        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(f"""
                SELECT * FROM documents
                {where_clause}
                ORDER BY created_at DESC
                LIMIT ?
            """, params + [limit])

            rows = cursor.fetchall()
            return [PayrollDocument(**dict(row)) for row in rows]

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get database statistics for dashboard.

        Returns:
            Dictionary with various statistics
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()

            # Total documents
            cursor.execute("SELECT COUNT(*) FROM documents")
            total_documents = cursor.fetchone()[0]

            # Documents by status
            cursor.execute("""
                SELECT processing_status, COUNT(*)
                FROM documents
                GROUP BY processing_status
            """)
            status_counts = dict(cursor.fetchall())

            # Documents by organization
            cursor.execute("""
                SELECT organization, COUNT(*)
                FROM documents
                WHERE organization IS NOT NULL
                GROUP BY organization
                ORDER BY COUNT(*) DESC
                LIMIT 10
            """)
            org_counts = dict(cursor.fetchall())

            # Issues by type
            cursor.execute("""
                SELECT issue_type, COUNT(*)
                FROM detected_issues
                GROUP BY issue_type
                ORDER BY COUNT(*) DESC
            """)
            issue_counts = dict(cursor.fetchall())

            # Recent activity (last 30 days)
            cursor.execute("""
                SELECT DATE(created_at) as date, COUNT(*)
                FROM documents
                WHERE created_at >= datetime('now', '-30 days')
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            """)
            recent_activity = dict(cursor.fetchall())

            return {
                "total_documents": total_documents,
                "status_counts": status_counts,
                "organization_counts": org_counts,
                "issue_counts": issue_counts,
                "recent_activity": recent_activity
            }
