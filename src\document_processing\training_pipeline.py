"""
End-to-end training pipeline for document processing with production-grade enhancements.
"""
import os
import glob
import datetime
import json
import logging
import sys
import shutil
from typing import List, Dict, Any, Optional
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import multiprocessing
import traceback
import time
from multiprocessing import cpu_count, Pool
import hashlib

import numpy as np

# Assuming these are defined in your project
from ..utils.logger import get_logger
from ..config import RAW_DIR, PROCESSED_DIR
from ..database.user_db import DocumentModel
from ..database.vector_store import QdrantVectorStore, VectorStoreError
from .file_processor import FileProcessor
from .text_chunker import TextChunker
from .embedding_generator import EmbeddingGenerator
from .version_control import DocumentVersionControl

logger = get_logger("TrainingPipeline")

CACHE_DIR = Path("data/cache")
CACHE_DIR.mkdir(parents=True, exist_ok=True)
PROCESSED_CHUNKS_CACHE = CACHE_DIR / "processed_chunks.json"

class DocumentProcessingException(Exception):
    """Custom exception for document processing errors."""
    def __init__(self, message: str, file_path: Optional[Path] = None, error_code: str = None):
        super().__init__(message)
        self.file_path = file_path
        self.error_code = error_code
        self.timestamp = datetime.datetime.now()

        # Log the exception immediately
        logger.error(f"DocumentProcessingException: {message}", extra={
            'file_path': str(file_path) if file_path else None,
            'error_code': error_code,
            'exception_type': 'DocumentProcessingException'
        })

class BaseEmbeddingGenerator:
    def generate_embeddings(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generates embeddings for a batch of document chunks.
        Expected input: List of dictionaries, each with a 'text' key.
        Expected output: Dictionary with 'embeddings' (List[List[float]]) and 'documents' (List[Dict[str, Any]]).
        The 'documents' list should correspond to the input batch, potentially with additional metadata.
        """
        raise NotImplementedError()

class BaseVectorStore:
    def add_documents(self, documents: List[Dict[str, Any]], embeddings: List[List[float]]) -> None:
        """
        Adds documents and their embeddings to the vector store.
        `documents` is expected to be a list of dictionaries containing chunk metadata (including 'id').
        `embeddings` is a list of corresponding embedding vectors.
        The 'content' (full text of the chunk) should NOT be stored here.
        """
        raise NotImplementedError()

class UnitOfWork:
    """Atomic operation boundary for DB and VectorStore updates."""
    def __init__(self, doc_model: DocumentModel, vector_store: BaseVectorStore):
        self.doc_model = doc_model
        self.vector_store = vector_store
        logger.info("UnitOfWork initialized successfully")

    def process_batch(self, batch: List[Dict[str, Any]], embedding_generator: BaseEmbeddingGenerator, file_name: str = "") -> int:
        batch_start_time = time.time()
        batch_id = f"{file_name}_{int(batch_start_time)}"

        logger.info("🔄 Starting batch processing", extra={
            'batch_id': batch_id,
            'batch_size': len(batch),
            'file_name': file_name
        })

        chunk_ids = []
        failed_chunks = []
        validated_batch = [] # Chunks that pass initial validation

        # ✅ Pre-validation: Chunks should already have 'text' and other required fields from previous stage
        for idx, chunk in enumerate(batch):
            required_keys = {"title", "content", "source_file", "chunk_index", "text"} # 'text' is now pre-validated
            if not all(key in chunk and chunk[key] is not None for key in required_keys):
                logger.error(f"❌ Chunk {idx+1} missing required fields or has None values", extra={
                    'batch_id': batch_id,
                    'chunk_index': idx,
                    'chunk_content_preview': str(chunk.get('content', ''))[:100], # Preview content
                    'missing_keys': [key for key in required_keys if key not in chunk or chunk[key] is None]
                })
                failed_chunks.append({
                    'chunk_index': idx,
                    'error': "Missing required keys or None values",
                    'chunk_preview': str(chunk.get('content', ''))[:100]
                })
                continue
            validated_batch.append(chunk)

        if not validated_batch:
            logger.error(f"🚫 All chunks invalid - aborting batch", extra={'batch_id': batch_id})
            return 0

        # Phase 1: Save to DB (full content stored here)
        logger.info(f"💾 Phase 1: Saving {len(validated_batch)} chunks to DB", extra={'batch_id': batch_id})
        chunks_for_embedding_and_vector_store = [] # This will hold a reduced set of metadata + DB ID

        for idx, chunk in enumerate(validated_batch):
            try:
                # Ensure all required fields are present and not None for DB insertion
                if not (chunk.get("title") and chunk.get("content") and chunk.get("source_file") and chunk.get("chunk_index") is not None):
                    raise ValueError("One or more required fields for DB insertion are missing or None")

                chunk_id = self.doc_model.save_document(
                    title=chunk["title"],
                    content=chunk["content"],
                    source_file=chunk["source_file"],
                    chunk_index=chunk["chunk_index"]
                )

                # Prepare a lighter version of the chunk for embedding and vector store
                # This explicitly excludes the 'content' field to prevent it from going into Qdrant
                chunk_for_vector_store = {
                    "id": chunk_id,
                    "title": chunk["title"],
                    "source_file": chunk["source_file"],
                    "chunk_index": chunk["chunk_index"],
                    "text": chunk["text"] # 'text' is used for embedding generation, not necessarily stored in Qdrant payload directly
                }
                chunks_for_embedding_and_vector_store.append(chunk_for_vector_store)
                logger.debug(f"✅ Chunk {idx+1} saved to DB and prepared for vector store", extra={'batch_id': batch_id, 'chunk_id': chunk_id})
            except Exception as e:
                failed_chunks.append({
                    'chunk_index': idx,
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'chunk_preview': str(chunk.get('content', ''))[:100]
                })
                logger.warning(f"❌ DB Save failed for chunk {idx+1}", extra={
                    'batch_id': batch_id,
                    'chunk_index': idx,
                    'error': str(e)
                })

        if not chunks_for_embedding_and_vector_store:
            logger.error("🚫 No chunks successfully saved to DB — aborting embedding/vector store for this batch", extra={'batch_id': batch_id})
            return 0

        # Phase 2: Embedding generation
        try:
            logger.info("🧠 Generating embeddings", extra={'batch_id': batch_id, 'chunk_count': len(chunks_for_embedding_and_vector_store)})
            embedding_start = time.time()

            # --- START OF FIX ---
            # The embedding model expects a list of strings, not a list of dictionaries.
            # We extract the 'text' from each chunk dictionary to create the list of strings.
            texts_to_embed = [f"passage: {chunk['text']}" for chunk in chunks_for_embedding_and_vector_store]

            # Pass the list of strings to the embedding generator.
            embeddings = embedding_generator.generate_embeddings(texts_to_embed)

            # The original chunk metadata (including the DB ID) is still needed for the vector store payload.
            documents_for_vector_store_payload = chunks_for_embedding_and_vector_store
            # --- END OF FIX ---


            if len(embeddings) != len(documents_for_vector_store_payload):
                raise ValueError("Mismatch between number of embeddings and documents returned by embedding generator.")

            embedding_duration = time.time() - embedding_start

            logger.info("✨ Embeddings generated", extra={
                'batch_id': batch_id,
                'count': len(embeddings),
                'dimension': embeddings.shape[1] if embeddings.shape[0] > 0 else 0,
                'time_seconds': round(embedding_duration, 2)
            })

            # Phase 3: Vector store insert
            logger.info(f"🗄️ Adding {len(documents_for_vector_store_payload)} chunks to vector store", extra={'batch_id': batch_id})

            # Ensure the documents passed to add_documents only contain what Qdrant should store as payload
            # Qdrant will store the vector itself, and then the payload. We don't want the raw 'text'/'content' in the payload.
            # The 'id' is crucial for retrieval from the main DB.
            qdrant_payloads = []
            for doc in documents_for_vector_store_payload:
                payload = {
                    "id": doc["id"],
                    "title": doc.get("title"),
                    "source_file": doc.get("source_file"),
                    "chunk_index": doc.get("chunk_index"),
                    "content": doc.get("text") # <--- ADD THIS LINE
                }
                qdrant_payloads.append(payload)
                
            self.vector_store.upload(qdrant_payloads, embeddings)

            total_time = time.time() - batch_start_time
            logger.info("✅ Batch completed", extra={
                'batch_id': batch_id,
                'total_chunks_processed': len(embeddings),
                'total_time_seconds': round(total_time, 2),
                'chunks_per_second': round(len(embeddings) / total_time, 2) if total_time else 0,
                'failed_chunks_in_batch': len(failed_chunks)
            })

            return len(embeddings)

        except Exception as embed_error:
            # --- MODIFIED ERROR LOGGING ---
            # The traceback will now correctly point to the line in this file if the error persists.
            logger.error("💥 Embedding/vector store failure | Error: %s", str(embed_error), extra={
                'batch_id': batch_id,
                'traceback': traceback.format_exc(),
                'orphaned_chunks_in_db': [c['id'] for c in chunks_for_embedding_and_vector_store]
            })
            # --- END OF MODIFICATION ---
            # TODO: Implement a rollback mechanism or a separate process to handle orphaned chunks
            # For now, we just log and return 0
            return 0


class TrainingPipeline:
    def __init__(self, vector_store=None, embedding_generator=None):
        self.chunker = TextChunker()
        self.embedding_generator = embedding_generator or EmbeddingGenerator()
        self.vector_store = vector_store or QdrantVectorStore()
        self.document_model = DocumentModel() # Initialize DocumentModel for database interaction
        self.batch_size = 8
        self.chunk_size = 300
        self.chunk_overlap = 50
        self.max_retries = 3
        self.hr_dir = RAW_DIR  # Assuming HR files are in RAW_DIR
        PROCESSED_DIR.mkdir(parents=True, exist_ok=True)
        logger.info(f"PROCESSED_DIR set to: {PROCESSED_DIR}")
        self.version_control = DocumentVersionControl()

    def is_file_processed(self, file_name: str) -> bool:
        """Checks if a file has already been processed end-to-end using the marker in PROCESSED_DIR."""
        marker_path = self._get_raw_file_marker_path(file_name)
        is_done = marker_path.exists()
        logger.debug("Checked file processed status", extra={"file_name": file_name, "processed": is_done})
        return is_done

    def _get_raw_file_marker_path(self, file_name_or_path) -> Path:
        """Returns the path for the marker file indicating raw file processing completion, in PROCESSED_DIR."""
        # Accept either a Path or a string
        if isinstance(file_name_or_path, Path):
            file_name = file_name_or_path.name
        else:
            file_name = file_name_or_path
        return PROCESSED_DIR / (file_name + ".processed_raw.json")

    def process_hr_files(self):
        """Process all HR-related documents in the HR directory, only if new or changed (hash-based)."""
        if not self.hr_dir.exists():
            logger.warning("HR directory does not exist", extra={"path": str(self.hr_dir)})
            return 0

        # Only process real HR files (skip .json and marker files)
        files = [f for f in self.hr_dir.iterdir() if f.is_file() and not f.name.startswith(".") and not f.suffix.lower() == ".json"]
        if not files:
            logger.info("No HR files found to process", extra={"path": str(self.hr_dir)})
            return 0

        logger.info("📁 Starting HR file processing (hash-based)", extra={'total_hr_files': len(files)})
        total_uploaded = 0

        for file_path in sorted(self.hr_dir.glob("*")):
            if not self._is_valid_file(file_path):
                continue
            # --- ADVANCED VERSION CONTROL ---
            if not self.version_control.is_file_changed(file_path):
                logger.info(f"⏩ Skipping HR file (unchanged): {file_path.name}")
                continue
            # ... process file as before ...
            processed = self._process_with_retry(file_path, force_reprocess=False)
            if processed:
                self.version_control.update_version(file_path)
                total_uploaded += processed

        logger.info("🏁 HR file processing complete", extra={'total_embeddings_uploaded': total_uploaded})
        
        # Auto-index all processed documents
        if total_uploaded > 0:
            try:
                from src.utils.index_documents import index_all_documents
                logger.info("🔍 Auto-indexing all processed documents...")
                index_success = index_all_documents()
                if index_success:
                    logger.info("✅ Auto-indexing completed for all documents")
                else:
                    logger.warning("⚠️ Auto-indexing failed for some documents")
            except Exception as e:
                logger.error(f"❌ Auto-indexing error: {e}")
        
        return total_uploaded


    def _get_processed_path(self, original_file_path: Path) -> Path:
        """Generates the path for the processed (chunked) JSON file."""
        # Use the original file name, but place it in PROCESSESSED_DIR with a .json suffix
        return PROCESSED_DIR / (original_file_path.name + ".chunks.json")

    def _write_processed_chunks(self, file_path: Path, chunks: List[Dict[str, Any]]):
        """Writes chunked data to a JSON file in the PROCESSED_DIR."""
        processed_file_path = self._get_processed_path(file_path)
        try:
            # When saving processed chunks, we can still save the full content if desired for re-processing
            # or debugging, but this file is intermediate and not the final vector store.
            with open(processed_file_path, 'w', encoding='utf-8') as f:
                json.dump(chunks, f, ensure_ascii=False, indent=2)
            logger.info("💾 Chunked data saved to PROCESSED_DIR", extra={'processed_file': str(processed_file_path), 'chunk_count': len(chunks)})
        except Exception as e:
            logger.error("❌ Failed to write processed chunks to file", extra={
                'file_path': str(processed_file_path),
                'error': str(e),
                'traceback': traceback.format_exc()
            })
            raise DocumentProcessingException(
                f"Failed to save processed chunks for {file_path.name}",
                file_path=file_path, error_code="CHUNK_SAVE_FAILED"
            )

    def _read_processed_chunks(self, file_path: Path) -> Optional[List[Dict[str, Any]]]:
        """Reads chunked data from a JSON file in the PROCESSED_DIR."""
        processed_file_path = self._get_processed_path(file_path)
        if not processed_file_path.exists():
            logger.debug("Processed chunks file not found", extra={'processed_file': str(processed_file_path)})
            return None
        try:
            with open(processed_file_path, 'r', encoding='utf-8') as f:
                chunks = json.load(f)
            logger.info("📚 Chunked data loaded from PROCESSED_DIR", extra={'processed_file': str(processed_file_path), 'chunk_count': len(chunks)})
            return chunks
        except json.JSONDecodeError as e:
            logger.error("❌ Failed to decode JSON from processed chunks file", extra={
                'file_path': str(processed_file_path),
                'error': str(e),
                'traceback': traceback.format_exc()
            })
            # Consider deleting corrupt file or re-processing
            return None
        except Exception as e:
            logger.error("❌ Failed to read processed chunks from file", extra={
                'file_path': str(processed_file_path),
                'error': str(e),
                'traceback': traceback.format_exc()
            })
            return None

    def process_directory(self, directory: Path, force_reprocess: bool = False) -> int:
        if not directory.exists():
            logger.error("📂 Directory does not exist", extra={'directory': str(directory)})
            return 0

        files = [f for f in directory.iterdir() if f.is_file() and not f.name.startswith(".")]
        if not files:
            logger.warning("📂 No files found in directory", extra={'directory': str(directory)})
            return 0

        logger.info("📁 Starting directory processing", extra={
            'directory': str(directory),
            'total_files': len(files),
            'force_reprocess': force_reprocess
        })

        total_processed = 0

        with ThreadPoolExecutor(max_workers=max(1, cpu_count() // 2)) as executor:
            futures = []
            for file_path in files:
                if not force_reprocess and self._is_already_processed_end_to_end(file_path):
                    logger.info("✅ Skipping already completely processed file (end-to-end)", extra={'file_name': file_path.name})
                    continue
                futures.append(executor.submit(self._process_with_retry, file_path, force_reprocess))

            for future in as_completed(futures):
                try:
                    processed = future.result()
                    total_processed += processed
                except DocumentProcessingException as e:
                    logger.error("💥 Skipping file after retries due to DocumentProcessingException", extra={'file': str(e.file_path), 'reason': e})
                except Exception as e:
                    logger.exception("❌ Unexpected error in file processing thread", extra={'error': str(e)})

        return total_processed

    def _is_already_processed_end_to_end(self, file_path: Path) -> bool:
        """
        Checks if the original raw file has been processed end-to-end (chunks saved, embeddings uploaded).
        This marker is created AFTER successful vector store upload.
        """
        marker_path = self._get_raw_file_marker_path(file_path)
        return marker_path.exists()

    def _write_processed_marker_end_to_end(self, file_path: Path, total_chunks: int, total_embeddings: int):
        """
        Writes a marker file to indicate that the original raw file has been fully processed
        (chunked, embedded, and added to vector store). This marker is placed in PROCESSED_DIR.
        """
        marker_path = self._get_raw_file_marker_path(file_path)
        data = {
            'file_name': file_path.name,
            'total_chunks': total_chunks,
            'total_embeddings': total_embeddings,
            'timestamp': time.time(),
            'status': 'completed_end_to_end'
        }
        try:
            with open(marker_path, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info("✅ End-to-end processing marker written", extra={'marker_file': str(marker_path)})
        except Exception as e:
            logger.error("❌ Failed to write end-to-end processing marker", extra={'marker_file': str(marker_path), 'error': str(e)})

    def _update_processed_chunks_cache(self, file_path: Path, document_chunks: list):
        """Append processed chunk_ids and file name to the cache file."""
        cache_data = []
        if PROCESSED_CHUNKS_CACHE.exists():
            with open(PROCESSED_CHUNKS_CACHE, 'r', encoding='utf-8') as f:
                try:
                    cache_data = json.load(f)
                except Exception:
                    cache_data = []
        for chunk in document_chunks:
            cache_data.append({
                'file_name': file_path.name,
                'chunk_index': chunk.get('chunk_index'),
                'chunk_id': chunk.get('chunk_id')
            })
        with open(PROCESSED_CHUNKS_CACHE, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=2)

    def _process_with_retry(self, file_path: Path, force_reprocess: bool) -> int:
        retries = 0
        last_error = None

        logger.info("🔄 Starting file processing with retry capability", extra={'file_name': file_path.name})

        while retries <= self.max_retries:
            try:
                if retries > 0:
                    wait_time = min(2 ** retries, 10)
                    logger.info("⏳ Retrying file processing", extra={
                        'file_name': file_path.name,
                        'retry_attempt': retries,
                        'wait_time_seconds': wait_time
                    })
                    time.sleep(wait_time)

                result = self.process_file(file_path, force_reprocess)
                return result

            except DocumentProcessingException as e:
                retries += 1
                last_error = e
                logger.warning("⚠️ File processing attempt failed (controlled exception)", extra={
                    'file_name': file_path.name,
                    'retry_attempt': retries,
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'error_code': e.error_code
                })
            except Exception as e:
                retries += 1
                last_error = e
                logger.warning("⚠️ File processing attempt failed (unexpected exception)", extra={
                    'file_name': file_path.name,
                    'retry_attempt': retries,
                    'error': str(e),
                    'error_type': type(e).__name__
                })

        logger.error("❌ All retry attempts exhausted", extra={
            'file_name': file_path.name,
            'final_error': str(last_error),
            'final_error_type': type(last_error).__name__ if last_error else None
        })

        raise DocumentProcessingException(
            f"Failed after {self.max_retries} retries: {str(last_error)}",
            file_path=file_path,
            error_code="MAX_RETRIES_EXCEEDED"
        )

    def _is_valid_file(self, file_path: Path) -> bool:
        """Enhanced file validation with better error handling."""
        try:
            # Check if file exists and is readable
            if not file_path.exists():
                logger.error(f"File does not exist: {file_path}")
                return False
                
            if not file_path.is_file():
                logger.error(f"Path is not a file: {file_path}")
                return False
                
            # Check file size (skip empty files)
            if file_path.stat().st_size == 0:
                logger.warning(f"File is empty: {file_path}")
                return False
                
            # Expanded list of supported file types
            valid_extensions = {
                '.pdf', '.docx', '.doc', '.txt', '.md', '.csv', 
                '.xls', '.xlsx', '.pptx', '.html', '.htm', 
                '.msg', '.jpg', '.jpeg', '.png', '.rtf'
            }
            
            file_extension = file_path.suffix.lower()
            
            if file_extension not in valid_extensions:
                logger.warning(f"Unsupported file type: {file_extension} for file: {file_path.name}")
                return False
                
            logger.info(f"File validation passed: {file_path.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error validating file {file_path}: {str(e)}")
            return False

    def process_file(self, file_path: Path, force_reprocess: bool) -> int:
        file_start_time = time.time()

        logger.info("📄 Starting file processing", extra={
        'file_name': file_path.name,
        'file_path': str(file_path),
        'file_size_bytes': file_path.stat().st_size if file_path.exists() else 0
        })

        if not self._is_valid_file(file_path):
            logger.warning("❌ File validation failed, skipping", extra={'file_name': file_path.name})
            return 0

        document_chunks = None
        processed_chunks_file_path = self._get_processed_path(file_path)

        # Attempt to load from PROCESSED_DIR first if not forcing re-process
        if not force_reprocess and processed_chunks_file_path.exists():
            document_chunks = self._read_processed_chunks(file_path)
            if document_chunks is not None:
                logger.info("✅ Loaded chunked data from PROCESSED_DIR", extra={'file_name': file_path.name, 'source': 'PROCESSED_DIR'})
            else:
                logger.warning("⚠️ Failed to load chunks from PROCESSED_DIR, will re-process raw file.", extra={'file_name': file_path.name})

        if document_chunks is None or force_reprocess: # If loading failed or force_reprocess is true
            try:
                logger.info("📖 Processing raw document to extract content", extra={'file_name': file_path.name})
                document = FileProcessor.process_file(file_path)
                content_length = len(document['content'])

                logger.info("📖 Document loaded successfully", extra={
                    'file_name': file_path.name,
                    'original_content_length': content_length
                })

                document_chunks = self.chunker.chunk_document(document)

                processed_chunk_ids = self._load_processed_chunks_cache()
                new_cache_entries = []
                deduped_chunks = []
                for i, chunk in enumerate(document_chunks):
                    if not isinstance(chunk, dict):
                        logger.error(f"Invalid chunk format at index {i} in file {file_path.name}: {chunk}")
                        raise DocumentProcessingException(
                            f"Chunking produced invalid format for {file_path.name} at index {i}",
                            file_path=file_path, error_code="INVALID_CHUNK_FORMAT"
                        )
                    chunk['text'] = str(chunk.get('content', ''))
                    # Use SHA256 for deduplication (stronger than MD5, 64 hex digits)
                    chunk['temp_id'] = hashlib.sha256(f"{file_path.name}-{chunk.get('chunk_index', i)}-{chunk['text']}".encode()).hexdigest()
                    chunk['chunk_id'] = chunk['temp_id']
                    logger.info("Generated chunk_id for chunk", extra={
                        'file_name': file_path.name,
                        'chunk_index': i,
                        'chunk_id': chunk['chunk_id']
                    })
                    if chunk['chunk_id'] in processed_chunk_ids:
                        logger.info("Skipping duplicate chunk (already processed)", extra={
                            'file_name': file_path.name,
                            'chunk_index': i,
                            'chunk_id': chunk['chunk_id']
                        })
                        continue
                    deduped_chunks.append(chunk)
                    new_cache_entries.append({
                        'file_name': file_path.name,
                        'chunk_index': i,
                        'chunk_id': chunk['chunk_id']
                    })
                if not deduped_chunks:
                    logger.info(f"All chunks for {file_path.name} are duplicates; skipping upload/embedding.")
                    return True
                # Save deduped chunks to processed dir, continue with embedding/upload
                self._write_processed_chunks(file_path, deduped_chunks)
                self._append_to_processed_chunks_cache(new_cache_entries)

            except Exception as e:
                logger.error("💥 Initial file parsing or chunking failed", extra={
                    'file_name': file_path.name,
                    'error': str(e),
                    'traceback': traceback.format_exc()
                })
                raise DocumentProcessingException(
                    f"Initial processing failed for {file_path.name}: {str(e)}",
                    file_path=file_path, error_code="FILE_PARSING_FAILED"
                )

        if not document_chunks:
            logger.error("🚫 No valid document chunks to process for embedding", extra={'file_name': file_path.name})
            return 0


        total_embeddings_uploaded = 0
        total_chunks = len(document_chunks)

        unit_of_work = UnitOfWork(self.document_model, self.vector_store)

        for i in range(0, total_chunks, self.batch_size):
            batch = document_chunks[i:i+self.batch_size]
            batch_num = i // self.batch_size + 1

            logger.info("📊 Processing batch with UnitOfWork", extra={
                'file_name': file_path.name,
                'batch_number': batch_num,
                'batch_size': len(batch)
            })

            try:
                # The UnitOfWork handles DB save, embedding, and vector store upload
                embeddings_in_batch = unit_of_work.process_batch(batch, self.embedding_generator, file_path.name)
                total_embeddings_uploaded += embeddings_in_batch
            except Exception as e:
                logger.error("💥 UnitOfWork batch processing failed", extra={
                    'file_name': file_path.name,
                    'batch_number': batch_num,
                    'error': str(e),
                    'traceback': traceback.format_exc()
                })
                # Decide if you want to re-raise or continue with other batches/files
                # For now, we log and let the outer retry mechanism handle it if necessary.
                # If a specific batch fails, total_embeddings_uploaded will just not increment for it.


        file_duration = time.time() - file_start_time

        # Write end-to-end processed marker only after all batches for this file are done
        if total_embeddings_uploaded > 0: # Only mark as fully processed if some embeddings were actually uploaded
            self._write_processed_marker_end_to_end(file_path, total_chunks, total_embeddings_uploaded)
            
            # Automatically index the processed document
            try:
                from src.utils.index_documents import index_specific_document
                logger.info(f"🔍 Auto-indexing document: {file_path.name}")
                index_success = index_specific_document(file_path.name)
                if index_success:
                    logger.info(f"✅ Auto-indexing completed for: {file_path.name}")
                else:
                    logger.warning(f"⚠️ Auto-indexing failed for: {file_path.name}")
            except Exception as e:
                logger.error(f"❌ Auto-indexing error for {file_path.name}: {e}")

        logger.info("🎯 File processing completed successfully", extra={
            'file_name': file_path.name,
            'total_chunks_processed': total_chunks, # Total chunks originally generated
            'total_embeddings_uploaded': total_embeddings_uploaded, # Total embeddings actually uploaded
            'processing_time_seconds': round(file_duration, 2)
        })

        return total_embeddings_uploaded # Return count of embeddings successfully uploaded

    def _load_processed_chunks_cache(self):
        """Load processed chunk_ids from the cache file as a set for fast lookup."""
        if PROCESSED_CHUNKS_CACHE.exists():
            with open(PROCESSED_CHUNKS_CACHE, 'r', encoding='utf-8') as f:
                try:
                    cache_data = json.load(f)
                    return set(entry['chunk_id'] for entry in cache_data)
                except Exception as e:
                    logger.warning(f"Failed to load processed_chunks.json: {e}")
                    return set()
        return set()

    def _append_to_processed_chunks_cache(self, new_entries):
        """Append new chunk_ids to the cache file."""
        cache_data = []
        if PROCESSED_CHUNKS_CACHE.exists():
            with open(PROCESSED_CHUNKS_CACHE, 'r', encoding='utf-8') as f:
                try:
                    cache_data = json.load(f)
                except Exception:
                    cache_data = []
        cache_data.extend(new_entries)
        with open(PROCESSED_CHUNKS_CACHE, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=2)

if __name__ == "__main__":
    # Ensure PROCESSED_DIR and RAW_DIR are properly configured in ..config
    # For testing, you might create dummy directories and files:
    # RAW_DIR.mkdir(parents=True, exist_ok=True)
    # (RAW_DIR / "example.txt").write_text("This is a sample document for processing. It has multiple sentences. Each sentence could be a chunk.")

    try:
        logger.info("🚀 Starting document processing pipeline")
        pipeline = TrainingPipeline()
        # Set force_reprocess=True for initial run or whenever you want to re-process everything
        # Set force_reprocess=False to leverage PROCESSED_DIR for skipping parse/chunking
        count = pipeline.process_directory(directory=RAW_DIR, force_reprocess=True)
        logger.info("🏁 Pipeline execution completed", extra={'total_documents_processed_with_embeddings': count})
        print(f"✅ Successfully processed and embedded {count} chunks across documents.")
    except KeyboardInterrupt:
        logger.info("⚠️ Pipeline interrupted by user")
        print("\n❌ Processing interrupted by user")
    except Exception as e:
        logger.error("💥 Pipeline failed with critical error", extra={
            'error': str(e),
            'traceback': traceback.format_exc()
        })
        print(f"❌ Critical error: {str(e)}")
        sys.exit(1)