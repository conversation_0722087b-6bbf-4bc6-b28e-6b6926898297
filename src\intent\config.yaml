# Intent Classifier Configuration - Production Optimized
model:
  # Base encoder settings
  encoder_name: "BAAI/bge-base-en-v1.5"
  max_sequence_length: 512
  hidden_size: 768
  
  # Fine-tuning settings
  fine_tune_encoder: true
  freeze_bottom_layers: 0  # Number of bottom layers to freeze (0 = no freezing)
  
  # Progressive unfreezing
  progressive_unfreezing:
    enabled: true
    unfreeze_schedule: [0.5, 0.75, 1.0]  # Unfreeze at 50%, 75%, 100% of training
    layer_groups: ["embeddings", "encoder_layers", "attention"]
  
  # Differential learning rates
  differential_lr:
    enabled: true
    lr_multipliers:
      embeddings: 0.1
      encoder_layers: 1.0
      attention: 2.0
      classifier: 10.0
  
  # Adapter-based fine-tuning (Phase 2)
  adapter:
    enabled: true  # Re-enable adapter fine-tuning
    adapter_type: "lora"  # lora, bitfit, none
    adapter_config:
      lora:
        rank: 16  # Reduced rank for stability
        alpha: 32
        dropout: 0.1
        bias: "none"  # none, all, lora_only
      bitfit:
        bias_only: true
        layer_norm_only: false
  
  # Classifier head architecture
  classifier_head:
    layers: [768, 512, 256]  # Hidden layer dimensions
    dropout_rates: [0.3, 0.2, 0.1]  # Dropout rates for each layer
    activation: "gelu"  # gelu, relu, swish
    use_batch_norm: true
    stochastic_depth: true  # Enable stochastic depth
    
  # Pooling strategy
  pooling_type: "attention"  # cls, mean, attention, adaptive
  
  # Multi-label classification
  multi_label: false
  
  # Two-stage classifier
  two_stage:
    enabled: false
    stage1_domains: ["hr_query", "task_command", "escalation", "general"]
    stage1_model_path: "data/models/stage1_classifier.pth"
    stage2_models_dir: "data/models/stage2_classifiers/"

# Hybrid Intent Reranking (Phase 2)
reranker:
  enabled: false
  top_k_candidates: 10
  rerank_score_formula: "alpha * classifier_conf + (1 - alpha) * retriever_score"
  alpha: 0.7  # Weight for classifier confidence vs retriever score
  similarity_threshold: 0.3

# Knowledge-Guided Classification (Phase 2)
knowledge_fusion:
  enabled: false
  fusion_strategy: "early"  # early, late, attention, none
  policy_embeddings_path: "data/policies/hr_policies.pt"
  fusion_config:
    early_fusion:
      concat_dim: 1
      projection_dim: 768
    late_fusion:
      weight_classifier: 0.7
      weight_policy: 0.3
    cross_attention:
      num_heads: 8
      dropout: 0.1

# Uncertainty Modeling (Phase 2)
uncertainty:
  enabled: false
  uncertainty_model: "mc_dropout"  # mc_dropout, dirichlet
  mc_dropout:
    dropout_passes: 10
    dropout_rate: 0.1
  dirichlet:
    concentration: 1.0
  entropy_threshold: 0.85
  fallback_log_path: "logs/fallback_logs.json"

# Intent Drift Detection (Phase 2)
drift_detection:
  enabled: false
  drift_threshold: 0.1
  detection_method: "kl_divergence"  # kl_divergence, cosine_shift
  monitoring_window: 1000  # Number of queries to monitor
  alert_threshold: 0.15
  drift_log_path: "logs/intent_drift.json"

# Loss function configuration
loss:
  type: "combined"  # cross_entropy, focal, label_smoothing, combined
  focal:
    alpha: 1.0
    gamma: 2.0
  label_smoothing:
    epsilon: 0.1
  contrastive:
    temperature: 0.1
    margin: 0.5
    weight: 0.3  # Weight for contrastive loss in combined loss
  combined:
    cross_entropy_weight: 0.7
    contrastive_weight: 0.3

# Training configuration - Production Optimized
training:
  batch_size: 32
  gradient_accumulation_steps: 2
  effective_batch_size: 64
  
  # Optimizer
  optimizer: "adamw"
  learning_rate: 2e-5
  weight_decay: 0.01
  warmup_steps: 100
  max_epochs: 5
  
  # Scheduler
  scheduler: "cosine"  # cosine, linear, cosine_with_restarts not supported
  scheduler_params:
    num_warmup_steps: 200
    num_training_steps: 2000
    num_cycles: 3  # Number of cosine restarts
    
  # Regularization
  gradient_clip_norm: 1.0
  mixed_precision: true
  gradient_centralization: true  # Enable gradient centralization
  
  # Early stopping
  early_stopping:
    enabled: true
    patience: 3  # Reduced from 5 for faster training
    min_delta: 0.001
    
  # Data augmentation - Reduced for faster training
  augmentation:
    enabled: true
    backtranslation: false  # Disabled for speed
    paraphrasing: true
    templating: true
    synonym_replacement: true
    max_augmentations_per_sample: 2  # Reduced from 3
    
    # Advanced augmentation
    mixup:
      enabled: false  # Disabled for speed
      alpha: 0.2
    cutmix:
      enabled: false  # Disabled for speed
      alpha: 1.0
    eda:
      enabled: true
      alpha_sr: 0.1  # Synonym replacement
      alpha_ri: 0.1  # Random insertion
      alpha_rs: 0.1  # Random swap
      alpha_rd: 0.1  # Random deletion
    
  # Hard negative mining
  hard_negative_mining:
    enabled: false  # Disabled for speed
    mining_ratio: 0.2  # 20% of batch will be hard negatives
    similarity_threshold: 0.7

# Cross-validation configuration
cross_validation:
  enabled: false  # Disabled for faster training
  n_folds: 5
  stratified: true
  shuffle: true
  random_state: 42

# Data configuration - Production Optimized
data:
  training_data_path: "data/training/intent_training_data.jsonl"
  validation_split: 0.10  # Reduced from 0.15 for faster training
  test_split: 0.05
  random_seed: 42
  
  # Data cleaning pipeline
  min_query_length: 5
  max_query_length: 200
  remove_duplicates: true
  
  # Data cleaning pipeline
  cleaning:
    enabled: true
    remove_duplicates: true
    remove_noisy_samples: true
    noise_threshold: 0.8  # Confidence threshold for noisy sample detection
    min_query_length: 5
    max_query_length: 200
    remove_special_chars: true
    normalize_whitespace: true
    
  # Class balancing
  class_balancing:
    enabled: true
    method: "oversample"  # oversample, undersample, focal_loss
    target_samples_per_class: 1000

# Evaluation configuration (Phase 2 Enhanced)
evaluation:
  metrics: ["accuracy", "precision", "recall", "f1", "confusion_matrix"]
  top_k_accuracy: [1, 3, 5]
  confidence_threshold: 0.5
  
  # Cross-validation evaluation
  cv_evaluation:
    enabled: false  # Disabled for faster training
    save_cv_results: true
    cv_results_path: "logs/cv_results.json"
  
  # Calibration
  calibration:
    enabled: false  # Disabled for faster training
    method: "temperature_scaling"
    validation_split: 0.1
    save_calibration_model: true
    calibration_model_path: "data/models/calibration_model.pt"
  
  # Phase 2 metrics
  advanced_metrics:
    jensen_shannon_divergence: false  # Disabled for speed
    expected_calibration_error: false  # Disabled for speed
    negative_log_likelihood: false  # Disabled for speed
    intent_heatmap: false  # Disabled for speed
    confused_pairs_analysis: false  # Disabled for speed
  
  # Visualization
  visualization:
    enabled: false  # Disabled for speed
    umap_tsne: false
    latent_space_plot: false
    confidence_distribution: false

# Logging and output - Production Optimized
logging:
  level: "INFO"
  log_dir: "logs/intent_classifier"
  save_checkpoints: true
  checkpoint_frequency: 1  # Save every N epochs
  save_best_model: true
  save_last_model: true
  
  # Tensorboard
  tensorboard: false  # Disabled for speed
  tensorboard_dir: "runs/intent_classifier"
  
  # Model saving
  model_dir: "./models/intent_classifier"
  save_best_model: true
  save_last_model: true
  
  # Phase 2 exports
  export_metrics: true
  metrics_export_path: "logs/metrics.json"
  intent_heatmap_path: "logs/intent_heatmap.csv"
  confused_pairs_path: "logs/confused_pairs.json"

# Explainability Configuration (Phase 2)
explainability:
  enabled: false  # Disabled for speed
  top_n_alternatives: 5
  confidence_threshold: 0.5
  uncertainty_threshold: 0.85
  return_attention: false
  
  # Confidence ranking
  confidence_ranking:
    enabled: false  # Disabled for speed
    rank_by: "confidence"  # confidence, uncertainty, entropy
    top_k_uncertain: 10
    top_k_confident: 10
  
  # Feature importance
  feature_importance:
    enabled: false  # Disabled for speed
    method: "attention"  # attention, gradient, integrated_gradients
    top_features: 10
  
  # Reasoning generation
  reasoning:
    enabled: false  # Disabled for speed
    template_based: true
    include_alternatives: true
    include_confidence: true

# Production settings
production:
  model_serving:
    batch_inference: true
    max_batch_size: 32
    timeout_seconds: 30
    
  # Monitoring
  monitoring:
    enabled: true
    metrics_export: true
    health_check_endpoint: true
    
  # Caching
  cache_embeddings: true
  cache_dir: "data/cache/embeddings"
  
  # Phase 2 production features
  explainability:
    enabled: false  # Disabled for speed
    top_n_alternatives: 5
    confidence_ranking: false
    uncertainty_reporting: false

# Advanced features
advanced:
  # Contrastive pretraining
  contrastive_pretraining:
    enabled: false
    pretraining_epochs: 5
    positive_pairs_file: "data/training/positive_pairs.jsonl"
    negative_pairs_file: "data/training/negative_pairs.jsonl"
    
  # Knowledge distillation
  knowledge_distillation:
    enabled: false
    teacher_model_path: "data/models/teacher_model.pth"
    temperature: 4.0
    alpha: 0.7
    
  # Adversarial training
  adversarial_training:
    enabled: false
    epsilon: 0.01
    alpha: 0.003
    steps: 3
  
  # Continual Learning (Phase 2)
  continual_learning:
    enabled: false
    few_shot_updates: true
    dynamic_labeling_queue: true
    queue_size: 1000
    periodic_refinetuning: true
    refinetuning_interval: 1000  # queries 