advanced:
  adversarial_training:
    alpha: 0.003
    enabled: false
    epsilon: 0.01
    steps: 3
  continual_learning:
    dynamic_labeling_queue: true
    enabled: false
    few_shot_updates: true
    periodic_refinetuning: true
    queue_size: 1000
    refinetuning_interval: 1000
  contrastive_pretraining:
    enabled: false
    negative_pairs_file: data/training/negative_pairs.jsonl
    positive_pairs_file: data/training/positive_pairs.jsonl
    pretraining_epochs: 5
  knowledge_distillation:
    alpha: 0.7
    enabled: false
    teacher_model_path: data/models/teacher_model.pth
    temperature: 4.0
cross_validation:
  enabled: false
  n_folds: 5
  random_state: 42
  shuffle: true
  stratified: true
data:
  class_balancing:
    enabled: true
    method: oversample
    target_samples_per_class: 1000
  cleaning:
    enabled: true
    max_query_length: 200
    min_query_length: 5
    noise_threshold: 0.8
    normalize_whitespace: true
    remove_duplicates: true
    remove_noisy_samples: true
    remove_special_chars: true
  max_query_length: 200
  min_query_length: 5
  random_seed: 42
  remove_duplicates: true
  test_split: 0.05
  training_data_path: data/training/intent_training_data.jsonl
  validation_split: 0.1
drift_detection:
  alert_threshold: 0.15
  detection_method: kl_divergence
  drift_log_path: logs/intent_drift.json
  drift_threshold: 0.1
  enabled: false
  monitoring_window: 1000
evaluation:
  advanced_metrics:
    confused_pairs_analysis: false
    expected_calibration_error: false
    intent_heatmap: false
    jensen_shannon_divergence: false
    negative_log_likelihood: false
  calibration:
    calibration_model_path: data/models/calibration_model.pt
    enabled: false
    method: temperature_scaling
    save_calibration_model: true
    validation_split: 0.1
  confidence_threshold: 0.5
  cv_evaluation:
    cv_results_path: logs/cv_results.json
    enabled: false
    save_cv_results: true
  metrics:
  - accuracy
  - precision
  - recall
  - f1
  - confusion_matrix
  top_k_accuracy:
  - 1
  - 3
  - 5
  visualization:
    confidence_distribution: false
    enabled: false
    latent_space_plot: false
    umap_tsne: false
explainability:
  confidence_ranking:
    enabled: false
    rank_by: confidence
    top_k_confident: 10
    top_k_uncertain: 10
  confidence_threshold: 0.5
  enabled: false
  feature_importance:
    enabled: false
    method: attention
    top_features: 10
  reasoning:
    enabled: false
    include_alternatives: true
    include_confidence: true
    template_based: true
  return_attention: false
  top_n_alternatives: 5
  uncertainty_threshold: 0.85
knowledge_fusion:
  enabled: false
  fusion_config:
    cross_attention:
      dropout: 0.1
      num_heads: 8
    early_fusion:
      concat_dim: 1
      projection_dim: 768
    late_fusion:
      weight_classifier: 0.7
      weight_policy: 0.3
  fusion_strategy: early
  policy_embeddings_path: data/policies/hr_policies.pt
logging:
  checkpoint_frequency: 1
  confused_pairs_path: logs/confused_pairs.json
  export_metrics: true
  intent_heatmap_path: logs/intent_heatmap.csv
  level: INFO
  log_dir: logs/intent_classifier
  metrics_export_path: logs/metrics.json
  model_dir: ./models/intent_classifier
  save_best_model: true
  save_checkpoints: true
  save_last_model: true
  tensorboard: false
  tensorboard_dir: runs/intent_classifier
loss:
  combined:
    contrastive_weight: 0.3
    cross_entropy_weight: 0.7
  contrastive:
    margin: 0.5
    temperature: 0.1
    weight: 0.3
  focal:
    alpha: 1.0
    gamma: 2.0
  label_smoothing:
    epsilon: 0.1
  type: combined
model:
  adapter:
    adapter_config:
      bitfit:
        bias_only: true
        layer_norm_only: false
      lora:
        alpha: 32
        bias: none
        dropout: 0.1
        rank: 16
    adapter_type: lora
    enabled: true
  classifier_head:
    activation: gelu
    dropout_rates:
    - 0.3
    - 0.2
    - 0.1
    layers:
    - 768
    - 512
    - 256
    stochastic_depth: true
    use_batch_norm: true
  differential_lr:
    enabled: true
    lr_multipliers:
      attention: 2.0
      classifier: 10.0
      embeddings: 0.1
      encoder_layers: 1.0
  encoder_name: BAAI/bge-base-en-v1.5
  fine_tune_encoder: true
  freeze_bottom_layers: 0
  hidden_size: 768
  max_sequence_length: 512
  multi_label: false
  pooling_type: attention
  progressive_unfreezing:
    enabled: true
    layer_groups:
    - embeddings
    - encoder_layers
    - attention
    unfreeze_schedule:
    - 0.5
    - 0.75
    - 1.0
  two_stage:
    enabled: false
    stage1_domains:
    - hr_query
    - task_command
    - escalation
    - general
    stage1_model_path: data/models/stage1_classifier.pth
    stage2_models_dir: data/models/stage2_classifiers/
production:
  cache_dir: data/cache/embeddings
  cache_embeddings: true
  explainability:
    confidence_ranking: false
    enabled: false
    top_n_alternatives: 5
    uncertainty_reporting: false
  model_serving:
    batch_inference: true
    max_batch_size: 32
    timeout_seconds: 30
  monitoring:
    enabled: true
    health_check_endpoint: true
    metrics_export: true
reranker:
  alpha: 0.7
  enabled: false
  rerank_score_formula: alpha * classifier_conf + (1 - alpha) * retriever_score
  similarity_threshold: 0.3
  top_k_candidates: 10
training:
  augmentation:
    backtranslation: false
    cutmix:
      alpha: 1.0
      enabled: false
    eda:
      alpha_rd: 0.1
      alpha_ri: 0.1
      alpha_rs: 0.1
      alpha_sr: 0.1
      enabled: true
    enabled: true
    max_augmentations_per_sample: 2
    mixup:
      alpha: 0.2
      enabled: false
    paraphrasing: true
    synonym_replacement: true
    templating: true
  batch_size: 32
  early_stopping:
    enabled: true
    min_delta: 0.001
    patience: 3
  effective_batch_size: 64
  gradient_accumulation_steps: 2
  gradient_centralization: true
  gradient_clip_norm: 1.0
  hard_negative_mining:
    enabled: false
    mining_ratio: 0.2
    similarity_threshold: 0.7
  learning_rate: 2e-5
  max_epochs: 5
  mixed_precision: true
  optimizer: adamw
  scheduler: cosine
  scheduler_params:
    num_cycles: 3
    num_training_steps: 2000
    num_warmup_steps: 200
  warmup_steps: 100
  weight_decay: 0.01
uncertainty:
  dirichlet:
    concentration: 1.0
  enabled: false
  entropy_threshold: 0.85
  fallback_log_path: logs/fallback_logs.json
  mc_dropout:
    dropout_passes: 10
    dropout_rate: 0.1
  uncertainty_model: mc_dropout
