"""
Optimized training script for intent classifier using HuggingFace Trainer API.
Production-grade training with mixed precision, checkpointing, and optimizations.
"""

import os
import torch
import torch.nn as nn
import numpy as np
import json
import yaml
import logging
import warnings
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from collections import Counter
import random
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from tqdm import tqdm
from datetime import datetime

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["WANDB_DISABLED"] = "true"  # Completely disable wandb

# HuggingFace imports
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification,
    TrainingArguments,
    Trainer,
    EarlyStoppingCallback,
    DataCollatorWithPadding
)

# Import local modules with fallbacks
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from intent_encoder import IntentEncoder, TwoStageIntentClassifier
except ImportError:
    IntentEncoder = None
    TwoStageIntentClassifier = None

try:
    from losses import get_loss_function
except ImportError:
    get_loss_function = None

try:
    from data_augmentation import DataAugmenter
except ImportError:
    DataAugmenter = None

try:
    from evaluate_classifier import IntentEvaluator
except ImportError:
    IntentEvaluator = None

logger = logging.getLogger(__name__)


def get_gpu_memory_gb():
    """Get available GPU memory in GB."""
    if torch.cuda.is_available():
        try:
            # Try to get memory info
            memory_info = torch.cuda.get_device_properties(0)
            total_memory_gb = memory_info.total_memory / (1024**3)
            logger.info(f"Detected GPU: {memory_info.name} with {total_memory_gb:.1f}GB memory")
            return total_memory_gb
        except Exception as e:
            logger.warning(f"Error getting GPU memory: {e}")
            return 0
    else:
        logger.warning("CUDA not available, using CPU")
        return 0


def get_dynamic_batch_size(gpu_memory_gb: float, override_batch_size: Optional[int] = None) -> int:
    """
    Determine optimal batch size based on available GPU memory.
    
    Args:
        gpu_memory_gb: Available GPU memory in GB
        override_batch_size: Optional CLI override
    
    Returns:
        Optimal batch size
    """
    if override_batch_size is not None:
        logger.info(f"Using CLI override batch size: {override_batch_size}")
        return override_batch_size
    
    if gpu_memory_gb >= 12:
        batch_size = 32
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB >= 12GB, using batch_size=32")
    elif gpu_memory_gb >= 8:
        batch_size = 16
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB (8-12GB), using batch_size=16")
    else:
        batch_size = 8
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB < 8GB, using batch_size=8")
    
    return batch_size


class IntentDataset:
    """Dataset for intent classification optimized for HuggingFace Trainer."""
    
    def __init__(self, texts: List[str], labels: List[int], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx]
        
        # Tokenize
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding=False,  # Let DataCollator handle padding
            max_length=self.max_length,
            return_tensors=None  # Return as lists, not tensors
        )
        
        return {
            'input_ids': encoding['input_ids'],
            'attention_mask': encoding['attention_mask'],
            'labels': label
        }


class OptimizedIntentTrainer:
    """
    Optimized trainer for intent classification using HuggingFace Trainer API.
    """
    
    def __init__(self, config_path: str, batch_size_override: Optional[int] = None, use_amp: bool = True):
        self.config = self._load_config(config_path)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.batch_size_override = batch_size_override
        self.use_amp = use_amp
        
        # Setup logging with reduced verbosity
        self._setup_logging()
        
        # Initialize model and tokenizer with caching
        self.model, self.tokenizer = self._initialize_model_and_tokenizer()
        
        # Initialize data augmenter with fallback
        if DataAugmenter is not None:
            augmentation_config = self.config.get('training', {}).get('augmentation', {})
            self.data_augmenter = DataAugmenter(augmentation_config)
        else:
            logger.warning("DataAugmenter not available, data augmentation will be disabled")
            self.data_augmenter = None
        
        # Initialize loss function with fallback
        if get_loss_function is not None:
            loss_config = self.config.get('training', {}).get('loss', {})
            self.loss_function = get_loss_function(loss_config)
        else:
            logger.warning("Custom loss functions not available, using default")
            self.loss_function = None
        
        # Initialize evaluator
        if IntentEvaluator is not None:
            evaluation_config = self.config.get('evaluation', {})
            self.evaluator = IntentEvaluator(evaluation_config)
        else:
            logger.warning("IntentEvaluator not available, evaluation will be limited")
            self.evaluator = None
        
        # Training state
        self.current_epoch = 0
        self.best_metric = 0.0
        self.patience_counter = 0
        
        # Initialize wandb if enabled and available
        # if self.config.get('wandb', {}).get('enabled', False) and WANDB_AVAILABLE:
        #     try:
        #         wandb.init(
        #             project=self.config['wandb']['project'],
        #             config=self.config,
        #             name=f"intent_classifier_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        #             mode="disabled"
        #         )
        #     except Exception as e:
        #         logger.warning(f"Failed to initialize wandb: {e}")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    
    def _setup_logging(self):
        """Setup logging configuration with reduced verbosity."""
        log_dir = Path(self.config.get('logging', {}).get('log_dir', './logs'))
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Reduce logging verbosity
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'training.log'),
                logging.StreamHandler()
            ]
        )
    
        # Suppress specific loggers
        logging.getLogger("transformers").setLevel(logging.WARNING)
        logging.getLogger("torch").setLevel(logging.WARNING)
        logging.getLogger("datasets").setLevel(logging.WARNING)
    
    def _initialize_model_and_tokenizer(self) -> Tuple[nn.Module, Any]:
        """Initialize model and tokenizer with caching."""
        model_config = self.config.get('model', {})
        model_name = model_config.get('encoder_name', 'BAAI/bge-base-en-v1.5')
        
        # Try to load from local cache first
        local_model_path = str(DATA_DIR / "models_cache" / model_name.split('/')[-1])
        local_tokenizer_path = str(DATA_DIR / "models_cache" / f"{model_name.split('/')[-1]}_tokenizer")
        
        try:
            if Path(local_model_path).exists() and Path(local_tokenizer_path).exists():
                logger.info(f"Loading model and tokenizer from local cache: {local_model_path}")
                model = AutoModelForSequenceClassification.from_pretrained(local_model_path)
                tokenizer = AutoTokenizer.from_pretrained(local_tokenizer_path)
            else:
                logger.info(f"Loading model and tokenizer from HuggingFace: {model_name}")
                # First load tokenizer to get vocabulary size
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                
                # Load model with default config first
                model = AutoModelForSequenceClassification.from_pretrained(model_name)
                
                # Cache locally for future use
                Path(local_model_path).mkdir(parents=True, exist_ok=True)
                Path(local_tokenizer_path).mkdir(parents=True, exist_ok=True)
                model.save_pretrained(local_model_path)
                tokenizer.save_pretrained(local_tokenizer_path)
                logger.info("Model and tokenizer cached locally")
        except Exception as e:
            logger.warning(f"Failed to load from cache, loading from HuggingFace: {e}")
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForSequenceClassification.from_pretrained(model_name)
        
        # Set pad token if not set
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        return model.to(self.device), tokenizer
    
    def _load_and_preprocess_data(self) -> Tuple[IntentDataset, IntentDataset, IntentDataset]:
        """Load and preprocess training data with optimized splits."""
        # Load raw data
        data_path = self.config['data']['training_data_path']
        texts, labels, label_encoder = self._load_training_data(data_path)
        
        # Set number of classes in model
        # num_classes = len(label_encoder.classes_)
        # self.model.num_labels = num_classes
        
        # Data cleaning
        texts, labels = self._clean_data(texts, labels)
        
        # Class balancing
        if self.config['data']['class_balancing']['enabled']:
            texts, labels = self._balance_classes(texts, labels)
        
        # Data augmentation
        if self.config['training']['augmentation']['enabled']:
            texts, labels = self._augment_data(texts, labels)
        
        # Optimized split: reduce validation to 10% for faster training
        val_split = 0.1  # Reduced from 0.15
        test_split = 0.05
        
        train_texts, temp_texts, train_labels, temp_labels = train_test_split(
            texts, labels, test_size=val_split + test_split,
            random_state=self.config['data']['random_seed'], stratify=labels
        )
        
        val_size = val_split / (val_split + test_split)
        val_texts, test_texts, val_labels, test_labels = train_test_split(
            temp_texts, temp_labels, test_size=1-val_size,
            random_state=self.config['data']['random_seed'], stratify=temp_labels
        )
        
        # Create datasets
        train_dataset = IntentDataset(train_texts, train_labels, self.tokenizer, 
                                    self.config['model']['max_sequence_length'])
        val_dataset = IntentDataset(val_texts, val_labels, self.tokenizer,
                                  self.config['model']['max_sequence_length'])
        test_dataset = IntentDataset(test_texts, test_labels, self.tokenizer,
                                   self.config['model']['max_sequence_length'])
        
        logger.info(f"Data loaded: {len(train_dataset)} train, {len(val_dataset)} val, {len(test_dataset)} test")
        
        return train_dataset, val_dataset, test_dataset
    
    def _load_training_data(self, data_path: str) -> Tuple[List[str], List[int], Any]:
        """Load training data from JSONL file with error handling."""
        texts = []
        intents = []
        
        with open(data_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    if 'query' in data and 'intent' in data:
                        texts.append(data['query'])
                        intents.append(data['intent'])
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping corrupted line {line_num}: {e}")
                    continue
                except Exception as e:
                    logger.warning(f"Error processing line {line_num}: {e}")
                    continue
        
        if len(texts) == 0:
            raise ValueError("No valid data found in the training file!")
        
        logger.info(f"Loaded {len(texts)} valid samples")
        
        # Create label encoder
        label_encoder = LabelEncoder()
        labels = label_encoder.fit_transform(intents)
        
        return texts, labels, label_encoder
    
    def _clean_data(self, texts: List[str], labels: List[int]) -> Tuple[List[str], List[int]]:
        """Clean and filter data."""
        config = self.config['data']
        
        # Get cleaning parameters with fallbacks
        min_length = config.get('min_query_length', 5)
        max_length = config.get('max_query_length', 200)
        remove_duplicates = config.get('remove_duplicates', True)
        
        cleaned_texts = []
        cleaned_labels = []
        
        for text, label in zip(texts, labels):
            if min_length <= len(text) <= max_length:
                cleaned_texts.append(text)
                cleaned_labels.append(label)
        
        # Remove duplicates if enabled
        if remove_duplicates:
            seen = set()
            unique_texts = []
            unique_labels = []
            for text, label in zip(cleaned_texts, cleaned_labels):
                if text not in seen:
                    seen.add(text)
                    unique_texts.append(text)
                    unique_labels.append(label)
            cleaned_texts, cleaned_labels = unique_texts, unique_labels
        
        logger.info(f"Data cleaned: {len(cleaned_texts)} samples")
        return cleaned_texts, cleaned_labels
    
    def _balance_classes(self, texts: List[str], labels: List[int]) -> Tuple[List[str], List[int]]:
        """Balance classes using oversampling/undersampling."""
        config = self.config['data']['class_balancing']
        method = config['method']
        target_samples = config['target_samples_per_class']
        
        # Count samples per class
        label_counts = Counter(labels)
        
        if method == 'oversample':
            # Oversample minority classes
            balanced_texts = []
            balanced_labels = []
            
            for label in set(labels):
                label_texts = [text for text, l in zip(texts, labels) if l == label]
                current_count = len(label_texts)
                
                if current_count < target_samples:
                    # Oversample
                    additional_samples = target_samples - current_count
                    oversampled = random.choices(label_texts, k=additional_samples)
                    label_texts.extend(oversampled)
                
                balanced_texts.extend(label_texts)
                balanced_labels.extend([label] * len(label_texts))
        
        elif method == 'undersample':
            # Undersample majority classes
            balanced_texts = []
            balanced_labels = []
            
            for label in set(labels):
                label_texts = [text for text, l in zip(texts, labels) if l == label]
                current_count = len(label_texts)
                
                if current_count > target_samples:
                    # Undersample
                    label_texts = random.sample(label_texts, target_samples)
                
                balanced_texts.extend(label_texts)
                balanced_labels.extend([label] * len(label_texts))
        
        else:
            balanced_texts, balanced_labels = texts, labels
        
        logger.info(f"Class balancing applied: {len(balanced_texts)} samples")
        return balanced_texts, balanced_labels
    
    def _augment_data(self, texts: List[str], labels: List[int]) -> Tuple[List[str], List[int]]:
        """Apply data augmentation."""
        augmented_texts = []
        augmented_labels = []
        
        for text, label in zip(texts, labels):
            augmented_texts.append(text)
            augmented_labels.append(label)
            
            # Generate augmentations
            if self.data_augmenter:
                augmentations = self.data_augmenter.augment(text)
                for aug_text in augmentations:
                    augmented_texts.append(aug_text)
                    augmented_labels.append(label)
        
        logger.info(f"Data augmentation applied: {len(augmented_texts)} samples")
        return augmented_texts, augmented_labels
    
    def _compute_metrics(self, eval_pred):
        """Compute metrics for HuggingFace Trainer."""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        
        # Calculate accuracy
        accuracy = (predictions == labels).astype(np.float32).mean()
        
        # Calculate F1 score
        from sklearn.metrics import f1_score
        f1 = f1_score(labels, predictions, average='weighted')
        
        return {
            'accuracy': accuracy,
            'f1': f1
        }
    
    def train(self):
        """Main training loop using HuggingFace Trainer."""
        logger.info("Starting optimized training...")
        
        # Load data
        train_dataset, val_dataset, test_dataset = self._load_and_preprocess_data()
        
        # Set number of classes in model AFTER loading data
        num_classes = len(set([item['labels'] for item in train_dataset]))
        logger.info(f"Detected {num_classes} classes, recreating model with correct classifier")
        
        # Recreate model with correct number of labels
        model_config = self.config.get('model', {})
        model_name = model_config.get('encoder_name', 'BAAI/bge-base-en-v1.5')
        model = AutoModelForSequenceClassification.from_pretrained(
            model_name, 
            num_labels=num_classes,
            ignore_mismatched_sizes=True
        )
        self.model = model.to(self.device)
        
        # Determine optimal batch size based on GPU memory
        gpu_memory_gb = get_gpu_memory_gb()
        optimal_batch_size = get_dynamic_batch_size(gpu_memory_gb, self.batch_size_override)
        
        # Log AMP status
        if self.use_amp:
            logger.info("Automatic Mixed Precision (AMP) enabled")
        else:
            logger.info("Automatic Mixed Precision (AMP) disabled")
        
        # Get training config with defaults
        training_config = self.config.get('training', {})
        
        # Setup training arguments with optimizations
        training_args = TrainingArguments(
            output_dir=str(DATA_DIR / "models" / "intent_classifier"),
            per_device_train_batch_size=optimal_batch_size,
            per_device_eval_batch_size=optimal_batch_size,
            gradient_accumulation_steps=2,
            learning_rate=float(training_config.get('learning_rate', 2e-5)),
            weight_decay=float(training_config.get('weight_decay', 0.01)),
            num_train_epochs=training_config.get('max_epochs', 10),
            warmup_steps=int(training_config.get('warmup_steps', 500)),
            fp16=self.use_amp,  # Enable mixed precision based on flag
            eval_strategy="steps",
            eval_steps=500,
            logging_steps=100,
            save_strategy="steps",
            save_steps=500,
            save_total_limit=2,  # Keep only 2 checkpoints
            load_best_model_at_end=True,
            metric_for_best_model="accuracy",
            greater_is_better=True,
            dataloader_num_workers=2,
            dataloader_pin_memory=True,
            gradient_checkpointing=True,  # Save memory
            remove_unused_columns=False,
            report_to=None,  # Completely disable wandb
            run_name=f"intent_classifier_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            # Optimizations
            dataloader_drop_last=True,
            group_by_length=True,  # Group similar length sequences
            length_column_name="length",
            # Logging optimizations
            logging_first_step=False,
            logging_dir="./logs",
            # Save optimizations
            save_on_each_node=False,
            save_safetensors=True,
        )
        
        # Setup data collator
        data_collator = DataCollatorWithPadding(
            tokenizer=self.tokenizer,
            padding=True,
            return_tensors="pt"
        )
        
        # Setup callbacks
        callbacks = []
        early_stopping_config = training_config.get('early_stopping', {})
        if early_stopping_config.get('enabled', False):
            callbacks.append(EarlyStoppingCallback(
                early_stopping_patience=early_stopping_config.get('patience', 3)
            ))
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
            compute_metrics=self._compute_metrics,
            callbacks=callbacks
        )
        
        # Train with checkpoint recovery
        try:
            # Check for existing checkpoint
            checkpoint_dir = DATA_DIR / "models" / "intent_classifier"
            checkpoints = list(checkpoint_dir.glob("checkpoint-*"))
            if checkpoints:
                latest_checkpoint = max(checkpoints, key=lambda x: int(x.name.split("-")[-1]))
                logger.info(f"Resuming from checkpoint: {latest_checkpoint}")
                trainer.train(resume_from_checkpoint=str(latest_checkpoint))
            else:
                trainer.train()
        except Exception as e:
            logger.error(f"Training failed: {e}")
            # Try to resume from latest checkpoint
            checkpoints = list(checkpoint_dir.glob("checkpoint-*"))
            if checkpoints:
                latest_checkpoint = max(checkpoints, key=lambda x: int(x.name.split("-")[-1]))
                logger.info(f"Attempting to resume from checkpoint: {latest_checkpoint}")
                trainer.train(resume_from_checkpoint=str(latest_checkpoint))
            else:
                raise e
        
        # Final evaluation on test set
        logger.info("Evaluating on test set...")
        test_results = trainer.evaluate(test_dataset)
        logger.info(f"Test results: {test_results}")
        
        # Save best model
        if self.config['logging']['save_best_model']:
            trainer.save_model(str(DATA_DIR / "models" / "intent_classifier_best"))
            logger.info("Best model saved to data/models/intent_classifier_best")
        
        logger.info("Training completed!")


def main():
    """Main training function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Train intent classifier with optimizations")
    parser.add_argument("--config", type=str, default="src/intent/config.yaml",
                       help="Path to configuration file")
    # parser.add_argument("--wandb", action="store_true", help="Enable wandb logging")
    parser.add_argument("--resume", action="store_true", help="Resume from checkpoint")
    parser.add_argument("--batch_size", type=int, default=None, 
                       help="Override batch size (optional, takes precedence over GPU memory detection)")
    parser.add_argument("--use_amp", action="store_true", default=True,
                       help="Enable Automatic Mixed Precision (AMP) [default: True]")
    parser.add_argument("--no_amp", action="store_true", 
                       help="Disable Automatic Mixed Precision (AMP)")
    
    args = parser.parse_args()
    
    # Handle AMP flag conflicts
    if args.no_amp:
        use_amp = False
    else:
        use_amp = args.use_amp
    
    # Load config
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # Enable wandb if requested
    # if args.wandb:
    #     config['wandb'] = {'enabled': True, 'project': 'intent-classifier', 'online': False}
    # else:
    #     # Disable wandb by default
    #     config['wandb'] = {'enabled': False}
    
    # Save config
    config_path = Path("src/intent/config_runtime.yaml")
    with open(config_path, 'w') as f:
        yaml.dump(config, f)
    
    # Initialize trainer and start training
    trainer = OptimizedIntentTrainer(
        str(config_path), 
        batch_size_override=args.batch_size,
        use_amp=use_amp
    )
    trainer.train()


if __name__ == "__main__":
    main() 