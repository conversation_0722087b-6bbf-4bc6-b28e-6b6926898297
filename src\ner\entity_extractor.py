"""
Named Entity Recognition (NER) module using transformers with SpanMarker model.
"""

import json
import logging
import os
import re
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field

# Disable wandb completely
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"

import torch
from transformers import AutoTokenizer, TrainingArguments, Trainer
from span_marker import SpanMarkerModel, Trainer as SpanMarkerTrainer
from datasets import Dataset
import numpy as np
from sklearn.metrics import classification_report, precision_recall_fscore_support

from ..utils.logger import get_logger
from ..config import DATA_DIR

logger = get_logger(__name__)


def get_gpu_memory_gb() -> float:
    """Get available GPU memory in GB."""
    if torch.cuda.is_available():
        return torch.cuda.get_device_properties(0).total_memory / (1024**3)
    else:
        logger.warning("CUDA not available, using CPU")
        return 0


def get_dynamic_batch_size(gpu_memory_gb: float, override_batch_size: Optional[int] = None) -> int:
    """
    Determine optimal batch size based on available GPU memory.
    
    Args:
        gpu_memory_gb: Available GPU memory in GB
        override_batch_size: Optional CLI override
    
    Returns:
        Optimal batch size
    """
    if override_batch_size is not None:
        logger.info(f"Using CLI override batch size: {override_batch_size}")
        return override_batch_size
    
    if gpu_memory_gb >= 12:
        batch_size = 32
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB >= 12GB, using batch_size=32")
    elif gpu_memory_gb >= 8:
        batch_size = 16
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB (8-12GB), using batch_size=16")
    else:
        batch_size = 8
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB < 8GB, using batch_size=8")
    
    return batch_size

# Paths
SPANMARKER_MODEL_PATH = Path("data/models_cache/spanmarker-deberta-v3-base-ner")
TRAINING_DATA_PATH = DATA_DIR / "training" / "entity_training_data.jsonl"
VALIDATION_DATA_PATH = DATA_DIR / "training" / "entity_validation_data.jsonl"
MODEL_OUTPUT_PATH = DATA_DIR / "models" / "ner_model"

# Ensure directories exist
MODEL_OUTPUT_PATH.mkdir(parents=True, exist_ok=True)


@dataclass
class Entity:
    """Represents a named entity."""
    text: str
    label: str
    start: int
    end: int
    confidence: float = 1.0


@dataclass
class EntityExtractionResult:
    """Result of entity extraction."""
    entities: List[Entity]
    processing_time: float
    model_used: str
    confidence_threshold: float = 0.5


class EntityExtractor:
    """
    Named Entity Recognition using SpanMarker model.
    """
    
    def __init__(self, model_path: Optional[str] = None, confidence_threshold: float = 0.5):
        """
        Initialize the entity extractor.
        
        Args:
            model_path: Path to SpanMarker model. Defaults to spanmarker-deberta-v3-base-ner.
            confidence_threshold: Minimum confidence for entity extraction.
        """
        self.model_path = model_path or str(SPANMARKER_MODEL_PATH)
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"Initializing EntityExtractor with SpanMarker model: {self.model_path}")
        logger.info(f"Using device: {self.device}")
        
        self._load_model()
        
        # Train model if training data exists and model doesn't exist
        if not Path(self.model_path).exists() and TRAINING_DATA_PATH.exists():
            record_count = sum(1 for _ in open(TRAINING_DATA_PATH))
            logger.info(f"Training data found with {record_count} records")
            logger.info("No pre-trained model found. Starting model training...")
            self.train_model()
    
    def _load_model(self):
        """Load the SpanMarker model."""
        try:
            if Path(self.model_path).exists():
                logger.info(f"Loading SpanMarker model from: {self.model_path}")

                # Load SpanMarker model
                self.model = SpanMarkerModel.from_pretrained(str(self.model_path))
                self.tokenizer = self.model.tokenizer

                # Move to GPU if available
                if self.device == "cuda":
                    self.model = self.model.cuda()
                    logger.info("Model moved to GPU")

                self.model.eval()
                logger.info("SpanMarker model loaded successfully")

                # Load entity types from config
                entity_types_path = Path(self.model_path) / "entity_types.json"
                if entity_types_path.exists():
                    with open(entity_types_path, 'r') as f:
                        entity_config = json.load(f)
                    self.entity_types = entity_config.get("entity_types", [])
                    logger.info(f"Available entity types: {self.entity_types}")
                else:
                    logger.warning("entity_types.json not found, using model config")
                    self.entity_types = list(self.model.config.id2label.values()) if hasattr(self.model, 'config') else []
            else:
                logger.warning(f"Model not found at: {self.model_path}. Using fallback mode.")
                # Initialize with a basic pre-trained model as fallback
                try:
                    logger.info("Attempting to load fallback SpanMarker model...")
                    self.model = SpanMarkerModel.from_pretrained("tomaarsen/span-marker-bert-base-fewnerd-fine-super")
                    self.tokenizer = self.model.tokenizer

                    if self.device == "cuda":
                        self.model = self.model.cuda()

                    self.model.eval()
                    self.entity_types = ["PER", "ORG", "LOC", "MISC"]  # Basic entity types
                    logger.info("Fallback SpanMarker model loaded successfully")
                except Exception as fallback_error:
                    logger.warning(f"Fallback model also failed: {fallback_error}. Using regex-only mode.")
                    self.model = None
                    self.tokenizer = None
                    self.entity_types = []
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            # Don't raise exception, allow system to work in regex-only mode
            self.model = None
            self.tokenizer = None
            self.entity_types = []
            logger.warning("Operating in regex-only mode without NER model")

    def extract_entities(self, text: str) -> EntityExtractionResult:
        """
        Extract named entities from text using SpanMarker.

        Args:
            text: Input text to extract entities from.

        Returns:
            EntityExtractionResult containing extracted entities and metadata.
        """
        if not text or not text.strip():
            return EntityExtractionResult(
                entities=[],
                processing_time=0.0,
                model_used=self.model_path
            )

        start_time = time.time()

        try:
            if self.model is not None:
                # Use SpanMarker to predict entities
                entities = self.model.predict(text.strip())

                # Convert to Entity objects
                entity_objects = []
                for entity in entities:
                    entity_obj = Entity(
                        text=entity["span"],
                        label=entity["label"],
                        start=entity["char_start_index"],
                        end=entity["char_end_index"],
                        confidence=entity.get("score", 1.0)
                    )
                    entity_objects.append(entity_obj)

                # Filter by confidence threshold
                filtered_entities = [
                    ent for ent in entity_objects
                    if ent.confidence >= self.confidence_threshold
                ]
            else:
                # Fallback to basic regex-based entity extraction
                logger.warning("No NER model available, using basic regex extraction")
                filtered_entities = self._basic_regex_extraction(text)

            processing_time = time.time() - start_time

            logger.info(f"Extracted {len(filtered_entities)} entities from text in {processing_time:.3f}s")

            return EntityExtractionResult(
                entities=filtered_entities,
                processing_time=processing_time,
                model_used=self.model_path if self.model else "regex-fallback",
                confidence_threshold=self.confidence_threshold
            )

        except Exception as e:
            logger.error(f"Entity extraction failed: {e}")
            return EntityExtractionResult(
                entities=[],
                processing_time=time.time() - start_time,
                model_used=self.model_path
            )

    def _basic_regex_extraction(self, text: str) -> List[Entity]:
        """Basic regex-based entity extraction as fallback."""
        entities = []

        # Simple patterns for common entities
        patterns = {
            "PERSON": r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',
            "ORG": r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:Ltd|Inc|Corp|Company|Organization)\b',
            "MONEY": r'(?:₹|Rs\.?|INR)\s*[\d,]+(?:\.\d{2})?',
            "DATE": r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b',
        }

        for label, pattern in patterns.items():
            for match in re.finditer(pattern, text):
                entity = Entity(
                    text=match.group(),
                    label=label,
                    start=match.start(),
                    end=match.end(),
                    confidence=0.6  # Lower confidence for regex matches
                )
                entities.append(entity)

        return entities
    
    def extract_entities_batch(self, texts: List[str]) -> List[EntityExtractionResult]:
        """
        Extract entities from multiple texts efficiently.
        
        Args:
            texts: List of input texts.
            
        Returns:
            List of EntityExtractionResult objects.
        """
        if not texts:
            return []
        
        start_time = time.time()
        results = []
        
        try:
            for text in texts:
                result = self.extract_entities(text)
                results.append(result)
            
            total_time = time.time() - start_time
            logger.info(f"Processed {len(texts)} texts in {total_time:.3f}s")
            
        except Exception as e:
            logger.error(f"Batch entity extraction failed: {e}")
            # Return empty results for all texts
            results = [
                EntityExtractionResult(
                    entities=[],
                    processing_time=0.0,
                    model_used=self.model_path
                )
                for _ in texts
            ]
        
        return results
    
    def train_model(self, training_data_path: Optional[str] = None,
                   validation_data_path: Optional[str] = None,
                   output_path: Optional[str] = None,
                   num_epochs: int = 5,
                   batch_size: Optional[int] = None,
                   learning_rate: float = 5e-5):
        """
        Train the model on custom HR data using SpanMarker's native training API.

        Args:
            training_data_path: Path to training data JSONL file.
            validation_data_path: Path to validation data JSONL file.
            output_path: Path to save the trained model.
            num_epochs: Number of training epochs.
            batch_size: Training batch size.
            learning_rate: Learning rate for training.
        """
        training_path = training_data_path or str(TRAINING_DATA_PATH)
        validation_path = validation_data_path or str(VALIDATION_DATA_PATH)
        model_output = output_path or str(MODEL_OUTPUT_PATH)

        if not Path(training_path).exists():
            logger.error(f"Training data not found: {training_path}")
            raise FileNotFoundError(f"Training data not found: {training_path}")

        logger.info(f"Starting model training...")
        logger.info(f"Training data: {training_path}")
        logger.info(f"Validation data: {validation_path}")
        logger.info(f"Output path: {model_output}")

        try:
            # Load and prepare training data
            train_dataset = self._load_training_data(training_path)
            logger.info(f"Loaded {len(train_dataset)} training samples")

            # Load validation data if available
            eval_dataset = None
            if validation_path and Path(validation_path).exists():
                eval_dataset = self._load_training_data(validation_path)
                logger.info(f"Loaded {len(eval_dataset)} validation samples")
            else:
                logger.warning("No validation data found. Training without validation.")

            # Initialize a fresh model for training
            base_model_name = "bert-base-uncased"
            logger.info(f"Initializing new SpanMarker model from {base_model_name}")

            # Get unique labels from training data
            all_labels = set()
            for item in train_dataset:
                for tag in item["ner_tags"]:
                    all_labels.add(tag)

            if eval_dataset:
                for item in eval_dataset:
                    for tag in item["ner_tags"]:
                        all_labels.add(tag)

            labels = sorted(list(all_labels))
            logger.info(f"Found {len(labels)} unique entity labels: {labels}")

            # Determine optimal batch size based on GPU memory
            gpu_memory_gb = get_gpu_memory_gb()
            optimal_batch_size = get_dynamic_batch_size(gpu_memory_gb, batch_size)
            logger.info(f"Using batch size: {optimal_batch_size}")

            # Create new model
            model = SpanMarkerModel.from_pretrained(
                base_model_name,
                labels=labels,
                model_max_length=1024,
                entity_max_length=12,
                mean_resizing=False  # Disable mean resizing warning
            )

            # Move to device
            if self.device == "cuda":
                model = model.cuda()
                logger.info("Model moved to GPU")

            # Create output directory
            Path(model_output).mkdir(parents=True, exist_ok=True)

            # Create trainer
            args = TrainingArguments(
                output_dir=model_output,
                learning_rate=learning_rate,
                per_device_train_batch_size=optimal_batch_size,
                per_device_eval_batch_size=optimal_batch_size,
                num_train_epochs=num_epochs,
                warmup_ratio=0.1,
                bf16=False,
                fp16=torch.cuda.is_available(),
                gradient_checkpointing=False,
                dataloader_num_workers=0,
                save_strategy="epoch",
                eval_strategy="epoch" if eval_dataset else "no",
                logging_steps=50,
                save_total_limit=2,
                load_best_model_at_end=True if eval_dataset else False,
                metric_for_best_model="eval_f1" if eval_dataset else None,
                greater_is_better=True,
                report_to="none",  # Disable wandb and other logging
                remove_unused_columns=False,
            )

            trainer = SpanMarkerTrainer(
                model=model,
                train_dataset=train_dataset,
                eval_dataset=eval_dataset,
                args=args
            )

            # Train the model
            logger.info("Starting training...")
            trainer.train()

            # Evaluate the model
            if eval_dataset:
                logger.info("Evaluating model...")
                eval_results = trainer.evaluate()
                logger.info(f"Evaluation results: {eval_results}")

            # Save the model
            logger.info(f"Saving model to {model_output}")
            trainer.save_model(model_output)

            # Save entity types for reference
            entity_types_path = Path(model_output) / "entity_types.json"
            with open(entity_types_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "entity_types": labels,
                    "encoder": base_model_name,
                    "total_entity_types": len(labels)
                }, f, indent=2)

            logger.info(f"Training completed successfully. Model saved to: {model_output}")

            # Load the trained model
            self.model_path = model_output
            self._load_model()

            return {
                "model_path": model_output,
                "entity_types": labels,
                "training_samples": len(train_dataset),
                "validation_samples": len(eval_dataset) if eval_dataset else 0,
                "epochs": num_epochs
            }

        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise

    def _load_training_data(self, data_path: str) -> List[Dict[str, Any]]:
        """
        Load and convert training data to SpanMarker format.

        Args:
            data_path: Path to JSONL training data file.

        Returns:
            List of training samples in SpanMarker format with 'tokens' and 'ner_tags' columns.
        """
        training_data = []

        with open(data_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    text = data["text"]
                    entities = data["entities"]

                    # Convert text to tokens and entities to BIO tags
                    tokens, ner_tags = self._convert_to_bio_format(text, entities, line_num)

                    if tokens and ner_tags:
                        training_data.append({
                            "tokens": tokens,
                            "ner_tags": ner_tags
                        })

                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error at line {line_num}: {e}")
                except KeyError as e:
                    logger.error(f"Missing required key {e} at line {line_num}")
                except Exception as e:
                    logger.error(f"Error processing line {line_num}: {e}")

        return training_data

    def _convert_to_bio_format(self, text: str, entities: List[List], line_num: int) -> Tuple[List[str], List[str]]:
        """
        Convert text and entities to BIO format.

        Args:
            text: Input text
            entities: List of entities in format [start, end, label]
            line_num: Line number for error reporting

        Returns:
            Tuple of (tokens, ner_tags) in BIO format
        """
        # Simple whitespace tokenization
        tokens = text.split()
        ner_tags = ["O"] * len(tokens)

        # Calculate character positions for each token
        token_positions = []
        char_pos = 0
        for token in tokens:
            # Find the start position of this token in the text
            while char_pos < len(text) and text[char_pos].isspace():
                char_pos += 1
            start_pos = char_pos
            end_pos = start_pos + len(token)
            token_positions.append((start_pos, end_pos))
            char_pos = end_pos

        # Process entities and assign BIO tags
        for entity in entities:
            if len(entity) >= 3:
                # Handle different entity formats flexibly
                if len(entity) == 3:
                    start, end, label = entity
                elif len(entity) == 4:
                    # Handle 4-element format: [start, end, extra_info, label] or [start, extra_start, end, label]
                    if isinstance(entity[2], str):
                        # Format: [start, end, label, extra_info] - use first 3 elements
                        start, end, label = entity[0], entity[1], entity[2]
                    else:
                        # Format: [start, extra_start, end, label] - use start, end (skip middle), label
                        start, end, label = entity[0], entity[2], entity[3]
                else:
                    # For 5+ elements, take first, last-1, and last as start, end, label
                    start, end, label = entity[0], entity[-2], entity[-1]

                # Validate span
                if not (0 <= start < end <= len(text)):
                    logger.warning(f"Invalid span [{start}, {end}] for text length {len(text)} at line {line_num}")
                    continue

                # Find tokens that overlap with this entity
                entity_tokens = []
                for i, (token_start, token_end) in enumerate(token_positions):
                    # Check if token overlaps with entity span
                    if (token_start < end and token_end > start):
                        entity_tokens.append(i)

                # Assign BIO tags
                for i, token_idx in enumerate(entity_tokens):
                    if i == 0:
                        ner_tags[token_idx] = f"B-{label}"
                    else:
                        ner_tags[token_idx] = f"I-{label}"
            else:
                logger.warning(f"Skipping invalid entity format (need at least 3 elements): {entity} at line {line_num}")

        return tokens, ner_tags

    def get_entity_types(self) -> List[str]:
        """Get list of entity types the model can recognize."""
        if not self.model:
            return []
        
        # Return loaded entity types (excluding "O")
        if hasattr(self, 'entity_types'):
            return [et for et in self.entity_types if et != "O"]
        
        # Fallback to model config
        if hasattr(self.model, 'config') and hasattr(self.model.config, 'id2label'):
            labels = list(self.model.config.id2label.values())
            # Remove B- and I- prefixes
            entity_types = set()
            for label in labels:
                if label.startswith("B-"):
                    entity_types.add(label[2:])
                elif label.startswith("I-"):
                    entity_types.add(label[2:])
            return list(entity_types)
        return []
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if not self.model:
            return {"error": "No model loaded"}
        
        return {
            "model_path": self.model_path,
            "entity_types": self.get_entity_types(),
            "device": self.device,
            "model_type": "SpanMarker",
            "available_labels": list(self.model.config.id2label.values()) if hasattr(self.model, 'config') else []
        }


# Test the entity extractor
if __name__ == "__main__":
    print("Testing transformers entity extractor...")

    # Initialize extractor
    extractor = EntityExtractor()
    
    # Test text
    test_text = "I need to apply for sick leave for 2 days starting tomorrow."
    
    # Extract entities
    result = extractor.extract_entities(test_text)
    
    print(f"Model info: {extractor.get_model_info()}")
    print(f"Extracted entities: {len(result.entities)}")
    for entity in result.entities:
        print(f"  - {entity.text} ({entity.label}) at positions {entity.start}-{entity.end}")
    
    print(f"Processing time: {result.processing_time:.3f}s")