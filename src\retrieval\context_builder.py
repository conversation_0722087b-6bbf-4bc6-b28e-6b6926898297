import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Callable, Union
from functools import wraps # Unused in the provided snippet but generally useful
import json # Unused but often useful for complex data structures
from dataclasses import dataclass, asdict
import math # For math.isclose
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

from ..utils.logger import get_logger
from .vector_search import VectorSearch, SearchServiceError, EmbeddingGenerationError, VectorStoreSearchError # Import specific exceptions from VectorSearch

logger = get_logger(__name__)

@dataclass
class ContextResult:
    """Structured result from context building operation."""
    context: str
    sources: List[Dict[str, Any]]
    total_chars: int
    total_tokens: int
    elapsed_time: float
    query_enhanced: bool
    documents_found: int
    documents_used: int

class ContextBuilderError(Exception):
    """Base exception for context builder errors."""
    pass

class DocumentRetrievalError(ContextBuilderError):
    """Raised when document retrieval fails."""
    pass

class TokenizationError(ContextBuilderError):
    """Raised when tokenization fails or returns an invalid value."""
    pass

class ContextBuilder:
    """Production-grade context builder with comprehensive error handling, monitoring, and validation."""

    # Default policy keywords mapping for query enhancement
    DEFAULT_POLICY_KEYWORDS = {
        "leave": ["leave", "vacation", "time off", "sick", "absence", "pto", "holiday"],
        "termination": ["termination", "firing", "layoff", "severance", "dismissal", "exit"],
        "dress code": ["dress", "attire", "clothing", "appearance", "uniform", "grooming"],
        "referral": ["referral", "refer", "recommendation", "recommend", "bonus"],
        "remote work": ["work from home", "wfh", "remote", "telework", "telecommute", "hybrid"],
        "salary": ["salary", "compensation", "pay", "wage", "ctc", "benefits", "appraisal"],
        "policy": ["policy", "rule", "guideline", "procedure", "regulation"],
        "resume": ["resume", "cv", "curriculum vitae", "bio-data", "biodata", "profile", "candidate profile"]
    }

    def __init__(
        self,
        vector_search: Optional[VectorSearch] = None,
        tokenizer: Optional[Callable[[str], int]] = None,
        policy_keywords: Optional[Dict[str, List[str]]] = None,
        monitoring_hook: Optional[Callable[[str, Union[float, int]], None]] = None,
        max_retries: int = 3,
        retry_base_delay: float = 1.0,
        max_retry_delay: float = 10.0,
        min_content_length: int = 50,
        sentence_cutoff_enabled: bool = True
    ):
        """
        Initialize the ContextBuilder.

        Args:
            vector_search (Optional[VectorSearch]): An instance of VectorSearch for document retrieval.
                                                    If None, a new instance will be created.
            tokenizer (Optional[Callable[[str], int]]): A function that takes a string and returns its token count.
                                                        If None, a simple word-based tokenizer is used.
            policy_keywords (Optional[Dict[str, List[str]]]): Custom mapping of policy types to keywords for query enhancement.
                                                               Defaults to DEFAULT_POLICY_KEYWORDS.
            monitoring_hook (Optional[Callable[[str, Union[float, int]], None]]): A callable for emitting metrics.
                                                                                  Takes metric_name (str) and value (float/int).
            max_retries (int): Maximum number of retries for internal operations (e.g., vector search). Must be >= 1.
            retry_base_delay (float): Base delay in seconds for exponential backoff retries. Must be > 0.
            max_retry_delay (float): Maximum delay in seconds for exponential backoff retries.
            min_content_length (int): Minimum character length for a document chunk to be included in the context. Must be >= 0.
            sentence_cutoff_enabled (bool): If True, attempts to truncate content at sentence boundaries.
        """
        try:
            self.vector_search = vector_search or VectorSearch()
            self.tokenizer = self._safe_tokenizer(tokenizer)
            self.policy_keywords = policy_keywords or self.DEFAULT_POLICY_KEYWORDS.copy() # Use .copy() to avoid modifying class default
            self.monitoring_hook = monitoring_hook
            self.max_retries = max_retries
            self.retry_base_delay = retry_base_delay
            self.max_retry_delay = max_retry_delay
            self.min_content_length = min_content_length
            self.sentence_cutoff_enabled = sentence_cutoff_enabled

            # Load reranker model and tokenizer
            self.reranker_model = AutoModelForSequenceClassification.from_pretrained("data/models_cache/bge-reranker-base")
            self.reranker_tokenizer = AutoTokenizer.from_pretrained("data/models_cache/bge-base-en-v1.5")
            self.reranker_model.eval()
            self.reranker_device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.reranker_model.to(self.reranker_device)

            self._validate_config()

            logger.info(
                f"ContextBuilder initialized with {len(self.policy_keywords)} policy types, "
                f"max_retries={max_retries}, min_content_length={min_content_length} chars, "
                f"sentence_cutoff_enabled={sentence_cutoff_enabled}"
            )
        except Exception as e:
            logger.critical(f"ContextBuilder initialization failed: {e}", exc_info=True)
            raise ContextBuilderError(f"Failed to initialize ContextBuilder: {e}") from e

    def _validate_config(self) -> None:
        """Validate configuration parameters to ensure operational stability."""
        if not isinstance(self.max_retries, int) or self.max_retries < 0:
            raise ValueError("max_retries must be a non-negative integer.")
        if not isinstance(self.retry_base_delay, (int, float)) or self.retry_base_delay <= 0:
            raise ValueError("retry_base_delay must be a positive number.")
        if not isinstance(self.max_retry_delay, (int, float)) or self.max_retry_delay < self.retry_base_delay:
            raise ValueError("max_retry_delay must be greater than or equal to retry_base_delay.")
        if not isinstance(self.min_content_length, int) or self.min_content_length < 0:
            raise ValueError("min_content_length must be a non-negative integer.")
        if not isinstance(self.sentence_cutoff_enabled, bool):
            raise ValueError("sentence_cutoff_enabled must be a boolean.")
        if not isinstance(self.policy_keywords, dict):
            raise ValueError("policy_keywords must be a dictionary.")

    def _safe_tokenizer(self, tokenizer: Optional[Callable[[str], int]]) -> Callable[[str], int]:
        """
        Creates a safe tokenizer wrapper.
        If no tokenizer is provided, a simple word-based fallback is used.
        Ensures the tokenizer always returns a valid non-negative integer.
        """
        if tokenizer is None:
            # Fallback tokenizer: counts words (space-separated)
            def word_tokenizer(text: str) -> int:
                if not isinstance(text, str) or not text:
                    return 0
                return len(text.split())
            logger.warning("No tokenizer provided. Using simple word-count fallback.")
            return word_tokenizer

        def safe_wrapper(text: str) -> int:
            try:
                if not isinstance(text, str) or not text:
                    return 0
                result = tokenizer(text)
                # Ensure the tokenizer returns a sensible integer token count
                if not isinstance(result, (int, float)):
                    logger.warning(f"Tokenizer returned non-numeric result type ({type(result)}). Using fallback.", extra={'text_preview': text[:50]})
                    return len(text.split()) # Fallback to word count
                return max(0, int(result)) # Ensure it's an integer and non-negative
            except Exception as e:
                logger.warning(f"Custom tokenizer failed ({e}). Using word-count fallback.", exc_info=True, extra={'text_preview': text[:50]})
                return len(text.split()) if text else 0 # Fallback to word count

        return safe_wrapper

    def _calculate_char_limit(self, max_tokens: int) -> int:
        """
        Calculates an approximate character limit based on the maximum token budget.
        Aims to be conservative to avoid exceeding token limits.
        """
        # Default conservative estimate: 4 characters per token
        chars_per_token_estimate = 4 
        
        # Attempt to get a more accurate estimate if the tokenizer is available
        sample_text = "This is a sample sentence to estimate the average character to token ratio for better context building. " * 5 # Longer sample for better average
        try:
            sample_tokens = self.tokenizer(sample_text)
            if sample_tokens > 0:
                # Add a small buffer (e.g., 10%) to the estimated char/token ratio to be safe
                chars_per_token_estimate = math.ceil(len(sample_text) / sample_tokens * 1.1) 
                chars_per_token_estimate = max(2, chars_per_token_estimate) # Ensure minimum chars per token
            logger.debug(f"Calculated char_per_token_estimate: {chars_per_token_estimate}")
        except Exception as e:
            logger.debug(f"Could not calculate precise char/token ratio: {e}. Using default: {chars_per_token_estimate}", exc_info=True)

        # Calculate the character limit, ensuring it's not excessively small
        calculated_limit = max_tokens * chars_per_token_estimate
        return max(self.min_content_length * 2, calculated_limit) # Ensure it's at least enough for a couple of min_content_length chunks

    def _enhance_query(self, query: str) -> tuple[str, bool]:
        """
        Enhances the query with relevant policy context keywords if a match is found.
        This helps guide the vector search towards more specific documents.
        """
        if not isinstance(query, str) or not query.strip():
            logger.debug("Query is empty or not a string, skipping enhancement.")
            return query.strip(), False

        query_lower = query.lower().strip()

        for policy_type, keywords in self.policy_keywords.items():
            if any(keyword.lower() in query_lower for keyword in keywords if keyword): # Ensure keyword is not empty
                enhanced_query_text = f"{policy_type} policy: {query.strip()}"
                logger.info(f"Enhanced query with policy type '{policy_type}'", extra={'original_query': query, 'enhanced_query': enhanced_query_text})
                return enhanced_query_text, True
        
        logger.debug("Query not enhanced by policy keywords.")
        return query.strip(), False

    def _extract_file_names(self, files_info: Optional[List[Dict[str, Any]]]) -> List[str]:
        """
        Safely extracts unique file names from a list of file information dictionaries.
        Handles various possible dictionary keys for the file name.
        """
        if not files_info:
            return []

        file_names = set() # Use a set for automatic de-duplication
        for i, file_info in enumerate(files_info):
            if not isinstance(file_info, dict):
                logger.warning(f"Skipping invalid file_info entry at index {i}: Not a dictionary ({type(file_info)}).")
                continue

            # Prioritize 'source_file', then other common names
            name = (
                file_info.get('source_file') or
                file_info.get('name') or
                file_info.get('filename') or
                file_info.get('file_name')
            )

            if isinstance(name, str) and name.strip():
                file_names.add(name.strip())
            else:
                logger.warning(f"File name not found or invalid in file_info entry at index {i}: {file_info}")

        return list(file_names)

    def _build_source_info(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extracts and formats standardized source information from a document dictionary.
        Ensures fields are strings and provides sensible defaults.
        """
        return {
            "title": str(doc.get("title", "Untitled Document")).strip() or "Untitled Document",
            "source_file": str(doc.get("source_file", "Unknown File")).strip() or "Unknown File",
            "score": float(doc.get("score", 0.0)), # Ensure score is a float
            "chunk_id": str(doc.get("chunk_id", "")).strip() or None, # Optional unique chunk identifier
            "metadata": {k: v for k, v in doc.get("metadata", {}).items() if isinstance(k, str) and isinstance(v, (str, int, float, bool, list, dict))} # Basic metadata sanitization
        }

    def _emit_metric(self, metric_name: str, value: Union[float, int]) -> None:
        """
        Safely emits a numerical metric using the configured monitoring hook.
        Prevents errors in the hook from disrupting the main context building process.
        """
        if self.monitoring_hook is None:
            logger.debug(f"Monitoring hook not configured. Skipping metric '{metric_name}'.")
            return

        try:
            self.monitoring_hook(metric_name, value)
            logger.debug(f"Emitted metric '{metric_name}': {value}")
        except Exception as e:
            logger.warning(f"Failed to emit metric '{metric_name}' with value '{value}': {e}", exc_info=True)

    def _find_sentence_cutoff(self, text: str, limit: int) -> int:
        """
        Finds the optimal cutoff point within a text to respect a character limit
        while attempting to preserve sentence integrity.
        """
        if not self.sentence_cutoff_enabled or limit >= len(text):
            return min(limit, len(text)) # Return the full text if within limit or feature disabled

        # Look for common sentence endings slightly before the limit
        # Order matters: look for newlines first as they are strong breaks
        sentence_endings = ['. ', '.\n', '! ', '!\n', '? ', '?\n', '\n\n', '\n']
        best_cutoff = -1
        search_start_index = max(0, limit - 150) # Search backwards from limit, but not too far

        for ending in sentence_endings:
            pos = text.rfind(ending, search_start_index, limit)
            if pos != -1:
                # Prioritize stronger breaks, or those closer to the limit
                current_cutoff = pos + len(ending) - 1
                if current_cutoff <= limit: # Ensure cutoff is not beyond the limit
                    if best_cutoff == -1 or current_cutoff > best_cutoff: # Find the latest valid cutoff
                        best_cutoff = current_cutoff

        # If a good sentence ending is found, ensure it's not too far back
        if best_cutoff != -1 and best_cutoff >= limit * 0.7: # Accept if within 30% of the limit
            return best_cutoff

        # Fallback to nearest word boundary if no suitable sentence ending is found
        # or if the sentence ending is too far from the limit.
        word_pos = text.rfind(' ', 0, limit)
        if word_pos != -1 and word_pos >= limit * 0.8: # Only truncate at word boundary if it's close to the limit
            return word_pos
        
        # Final fallback: hard limit
        logger.debug(f"No suitable sentence/word cutoff found for limit {limit}, falling back to hard limit.")
        return limit # Fallback to hard cutoff

    async def build_context(
        self,
        query: str,
        max_tokens: int = 2000,
        files_info: Optional[List[Dict[str, Any]]] = None,
        response_mode: str = "concise"
    ) -> ContextResult:
        operation_start_time = time.time()

        # 1. Input Validation
        if not isinstance(query, str) or not query.strip():
            logger.error("Invalid or empty query provided to build_context.")
            raise ContextBuilderError("Query must be a non-empty string.")
        if not isinstance(max_tokens, int) or max_tokens <= 0:
            logger.error(f"Invalid max_tokens provided: {max_tokens}. Must be a positive integer.")
            raise ContextBuilderError("max_tokens must be a positive integer.")
        if files_info is not None and not isinstance(files_info, list):
            logger.warning(f"files_info provided is not a list ({type(files_info)}). Ignoring for now.")
            files_info = []

        logger.info(f"Starting context building for query: '{query[:100]}...' with max_tokens={max_tokens}", extra={'query_length': len(query)})

        # 2. Dynamic Search Parameters and Enhance Query
        response_mode = response_mode.lower().strip() if response_mode else "concise"
        if response_mode == "detailed":
            top_k_candidates = 8
            final_token_limit = 3500
        else:
            top_k_candidates = 4
            final_token_limit = 2500
        char_limit_for_context = self._calculate_char_limit(final_token_limit)

        enhanced_query, query_was_enhanced = self._enhance_query(query)
        prioritized_files = self._extract_file_names(files_info)

        if prioritized_files:
            logger.info(f"Prioritizing search for {len(prioritized_files)} distinct files.")

        # 3. Retrieve Documents
        retrieved_documents: List[Dict[str, Any]] = []
        try:
            logger.info(f"[build_context] About to call _search_documents_safe with query: '{enhanced_query[:50]}...'")
            retrieved_documents = await self._search_documents_safe(
                enhanced_query,
                top_k=top_k_candidates,
                prioritized_files=prioritized_files
            )
            logger.info(f"[build_context] _search_documents_safe completed, got {len(retrieved_documents)} documents")
        except DocumentRetrievalError as e:
            logger.error(f"Failed to retrieve documents for context: {e}", exc_info=True)
            return ContextResult(
                context="", sources=[], total_chars=0, total_tokens=0,
                elapsed_time=time.time() - operation_start_time,
                query_enhanced=query_was_enhanced, documents_found=0, documents_used=0
            )

        if not retrieved_documents and self.is_extraction_intent(query):
            logger.info("No relevant chunks found, but query is extraction-related. Returning all available document chunks as fallback.")
            all_chunks = []
            try:
                if prioritized_files:
                    for file in prioritized_files:
                        all_chunks.extend(await self.vector_search.get_all_chunks_for_file(file))
                else:
                    all_chunks = await self.vector_search.get_all_chunks()
            except Exception as e:
                logger.error(f"Failed to retrieve all chunks for fallback extraction: {e}")
            retrieved_documents = all_chunks

        if not retrieved_documents:
            logger.warning(f"No relevant documents found after retrieval and initial filtering for query: '{query[:100]}...'")
            logger.warning(f"Query was enhanced: {query_was_enhanced}, prioritized files: {prioritized_files}")
            self._emit_metric("context_build_no_documents", 1)
            return ContextResult(
                context="", sources=[], total_chars=0, total_tokens=0,
                elapsed_time=time.time() - operation_start_time,
                query_enhanced=query_was_enhanced, documents_found=0, documents_used=0
            )

        # Filter out chunks with less than 100 chars for reranking
        filtered_docs = [doc for doc in retrieved_documents if len(doc.get("content", "").strip()) >= 100]
        if not filtered_docs:
            filtered_docs = retrieved_documents  # fallback to all if none pass

        # If only one high-confidence doc, use it directly
        if len(filtered_docs) == 1 or (len(filtered_docs) > 0 and filtered_docs[0].get("score", 0) > 0.95):
            reranked_docs = filtered_docs[:1]
            reranker_scores = [filtered_docs[0].get("score", 0)]
        else:
            # Rerank using cross-encoder
            pairs = [f"{query} </s> {doc['content']}" for doc in filtered_docs]
            inputs = self.reranker_tokenizer(
                pairs,
                padding=True,
                truncation=True,
                return_tensors="pt",
                max_length=512
            )
            inputs = {k: v.to(self.reranker_device) for k, v in inputs.items()}
            with torch.no_grad():
                scores = self.reranker_model(**inputs).logits.squeeze(-1)
            # Manual relevance boost for direct query keyword match
            query_keywords = set(query.lower().split())
            boosted_docs = []
            for score, doc in zip(scores.tolist(), filtered_docs):
                title = doc.get("title", "").lower()
                content = doc.get("content", "").lower()
                if any(kw in title or kw in content for kw in query_keywords):
                    score += 0.3
                boosted_docs.append((score, doc))
            # Sort by boosted score
            boosted_docs.sort(key=lambda x: x[0], reverse=True)
            reranked_docs = [doc for score, doc in boosted_docs]
            reranker_scores = [score for score, doc in boosted_docs]

        # Log reranker scores and token counts for debugging
        for i, doc in enumerate(reranked_docs):
            token_count = self.reranker_tokenizer(doc.get("content", "")).__len__()
            logger.info(f"[RERANKED] Rank {i+1} | Title: {doc.get('title', '')} | Tokens: {token_count} | Score: {reranker_scores[i]:.4f}")

        # 4. Build Context String and Collect Sources (preserve top-ranked docs, never drop top-matching content)
        context_parts: List[str] = []
        unique_sources: Dict[str, Dict[str, Any]] = {}
        current_total_tokens = 0
        documents_actually_used = 0
        for doc in reranked_docs:
            content_to_add = doc.get("content", "").strip()
            if not content_to_add or len(content_to_add) < self.min_content_length:
                continue
            doc_tokens = self.reranker_tokenizer(content_to_add)
            doc_token_count = len(doc_tokens)
            if current_total_tokens + doc_token_count > final_token_limit:
                # Only break if adding this doc would exceed the budget and it's not a direct query match
                title = doc.get("title", "").lower()
                content = doc.get("content", "").lower()
                if any(kw in title or kw in content for kw in query.lower().split()):
                    # If direct match, allow overflow
                    pass
                else:
                    break
            context_parts.append(content_to_add)
            current_total_tokens += doc_token_count
            documents_actually_used += 1
            source_info = self._build_source_info(doc)
            source_key = f"{source_info['source_file']}|{source_info['title']}"
            if source_key not in unique_sources:
                unique_sources[source_key] = source_info
            else:
                if source_info['score'] > unique_sources[source_key]['score']:
                    unique_sources[source_key]['score'] = source_info['score']

        final_context_string = "\n\n---\n\n".join(context_parts).strip()
        # Final trim to token limit (in case of overflow from direct match)
        tokenized = self.reranker_tokenizer.encode(final_context_string, truncation=True, max_length=final_token_limit)
        trimmed_context = self.reranker_tokenizer.decode(tokenized, skip_special_tokens=True)
        final_sources_list = list(unique_sources.values())
        final_sources_list.sort(key=lambda x: x["score"], reverse=True)

        final_total_tokens = 0
        try:
            final_total_tokens = self.tokenizer(trimmed_context)
            if final_total_tokens > final_token_limit:
                 logger.warning(f"Final context token count ({final_total_tokens}) exceeded final_token_limit ({final_token_limit}). This might indicate tokenizer estimation variance or aggressive content inclusion.")
        except Exception as e:
            logger.error(f"Failed to count tokens for final context: {e}", exc_info=True)
            raise TokenizationError(f"Failed to tokenize final context: {e}") from e

        elapsed_time = time.time() - operation_start_time

        result = ContextResult(
            context=trimmed_context,
            sources=final_sources_list,
            total_chars=len(trimmed_context),
            total_tokens=final_total_tokens,
            elapsed_time=elapsed_time,
            query_enhanced=query_was_enhanced,
            documents_found=len(retrieved_documents),
            documents_used=documents_actually_used
        )

        logger.info(
            f"Context building complete: Chars={result.total_chars}, Tokens={result.total_tokens}, "
            f"Sources={len(result.sources)}, Docs Used={result.documents_used}/{result.documents_found} "
            f"in {result.elapsed_time:.3f}s. Query Enhanced: {result.query_enhanced}."
        )

        # 7. Emit Metrics
        self._emit_metric("context_build_time_seconds", result.elapsed_time)
        self._emit_metric("context_sources_count", len(result.sources))
        self._emit_metric("context_chars", result.total_chars)
        self._emit_metric("context_tokens", result.total_tokens)
        self._emit_metric("context_documents_used", result.documents_used)
        self._emit_metric("context_documents_found", result.documents_found)
        if result.documents_found > 0:
            self._emit_metric("context_efficiency_ratio", result.documents_used / result.documents_found)
        
        return result

    def format_context_with_sources(self, result: ContextResult) -> str:
        """
        Formats the built context string by appending a list of cited sources.
        """
        if not isinstance(result, ContextResult):
            logger.error("Invalid input: result must be a ContextResult instance.")
            return ""

        if not result.context.strip():
            logger.info("No context available to format.")
            return ""

        if not result.sources:
            logger.info("No sources available to append to context.")
            return result.context

        source_text_parts = ["\n\n--- Sources ---", ""] # Start with a clear separator
        for i, source in enumerate(result.sources, start=1):
            # Ensure source data is valid before using
            title = str(source.get("title", "Unknown Title")).strip() or "Unknown Title"
            source_file = str(source.get("source_file", "Unknown File")).strip() or "Unknown File"
            score = source.get("score", 0.0)
            relevance = f"{score:.3f}" if score is not None else "N/A" # Handle None score gracefully

            source_text_parts.append(f"{i}. **{title}** (File: {source_file}, Relevance: {relevance})")

        return result.context + "\n".join(source_text_parts)

    def get_context_stats(self, result: ContextResult) -> Dict[str, Any]:
        """
        Retrieves detailed statistics about the context building operation.
        """
        if not isinstance(result, ContextResult):
            logger.error("Invalid input: result must be a ContextResult instance.")
            return {}

        return {
            "query_enhanced": result.query_enhanced,
            "documents_found": result.documents_found,
            "documents_used": result.documents_used,
            "total_chars": result.total_chars,
            "total_tokens": result.total_tokens,
            "sources_count": len(result.sources),
            "elapsed_time_seconds": result.elapsed_time, # Explicitly name unit
            "efficiency_ratio": result.documents_used / max(1, result.documents_found),
            "has_context": bool(result.context.strip())
        }

    async def _search_documents_safe(
        self,
        query: str,
        top_k: int,
        prioritized_files: List[str]
    ) -> List[Dict[str, Any]]:
        """
        Performs the actual document search using VectorSearch, with robust error handling
        and validation of search results.
        """
        if not isinstance(query, str) or not query.strip():
            raise DocumentRetrievalError("Invalid or empty query for document search.")
        if not isinstance(top_k, int) or top_k <= 0:
            raise DocumentRetrievalError("top_k must be a positive integer for document search.")
        if not isinstance(prioritized_files, list):
            raise DocumentRetrievalError("prioritized_files must be a list.")

        logger.info(f"[_search_documents_safe] Starting vector search for query: '{query[:50]}...' with top_k={top_k}, prioritized_files={len(prioritized_files)}.")

        num_retries = 0
        while num_retries <= self.max_retries:
            try:
                if num_retries > 0:
                    wait_time = min(self.max_retry_delay, self.retry_base_delay * (2 ** (num_retries - 1)))
                    logger.warning(f"Retrying document search (attempt {num_retries+1}/{self.max_retries+1}) after {wait_time:.2f}s...", extra={'query_preview': query[:50]})
                    await asyncio.sleep(wait_time) # Use asyncio.sleep for async functions

                # Determine if vector_search.search is async or sync and call accordingly
                logger.info(f"[_search_documents_safe] About to call vector_search.search, is_async: {asyncio.iscoroutinefunction(self.vector_search.search)}")
                if asyncio.iscoroutinefunction(self.vector_search.search):
                    documents = await self.vector_search.search(
                        query,
                        top_k=top_k,
                        prioritize_files=prioritized_files
                    )
                else:
                    # Run synchronous search in a separate thread to avoid blocking the event loop
                    logger.info(f"[_search_documents_safe] Using run_in_executor for sync search")
                    loop = asyncio.get_running_loop() # Use get_running_loop() for Python 3.7+
                    documents = await loop.run_in_executor(
                        None, # Use default thread pool
                        lambda: self.vector_search.search(
                            query,
                            top_k=top_k,
                            prioritize_files=prioritized_files
                        )
                    )
                logger.info(f"[_search_documents_safe] vector_search.search completed, got {len(documents)} documents")
                
                # Basic validation of the returned structure from vector_search
                if not isinstance(documents, list):
                    logger.error(f"Vector search returned non-list type: {type(documents)}. Expected List[Dict].", extra={'query_preview': query[:50]})
                    raise ValueError("Vector search returned invalid data type.")

                # Filter and validate individual documents
                valid_documents = []
                for idx, doc in enumerate(documents):
                    if not isinstance(doc, dict):
                        logger.warning(f"Skipping invalid document entry (not a dict) at index {idx}: {doc}", extra={'query_preview': query[:50]})
                        continue
                    
                    # Ensure content is string and sufficient length
                    content = doc.get("content")
                    if not isinstance(content, str) or len(content.strip()) < self.min_content_length:
                        logger.debug(f"Skipping document with insufficient content length ({len(str(content).strip())} chars) at index {idx}.", extra={'doc_id': doc.get('id'), 'source_file': doc.get('source_file')})
                        continue
                    
                    # Ensure score is numeric
                    score = doc.get("score")
                    if not isinstance(score, (int, float)) or math.isnan(score) or math.isinf(score):
                         logger.warning(f"Skipping document with invalid score at index {idx}: {score}", extra={'doc_id': doc.get('id'), 'source_file': doc.get('source_file')})
                         continue

                    valid_documents.append(doc)
                
                logger.info(f"Successfully retrieved and validated {len(valid_documents)} documents from vector search (out of {len(documents)} raw results).")
                return valid_documents

            except (SearchServiceError, EmbeddingGenerationError, VectorStoreSearchError, ValueError) as e:
                # Catch specific errors from VectorSearch or our own validation errors
                logger.warning(f"Document search attempt failed with known error: {type(e).__name__} - {e}", exc_info=True, extra={'query_preview': query[:50]})
                num_retries += 1
                if num_retries > self.max_retries:
                    raise DocumentRetrievalError(f"Document search failed after {self.max_retries} retries due to: {e}") from e
            except Exception as e:
                # Catch any other unexpected errors
                logger.error(f"An unexpected error occurred during document search attempt: {type(e).__name__} - {e}", exc_info=True, extra={'query_preview': query[:50]})
                num_retries += 1
                if num_retries > self.max_retries:
                    raise DocumentRetrievalError(f"Document search failed after {self.max_retries} retries due to unexpected error: {e}") from e

        # This part should ideally not be reached if retries are handled and exceptions re-raised.
        # But as a fallback, ensure we raise if somehow we exit the loop without return/raise.
        raise DocumentRetrievalError("Document search failed due to an unhandled retry exhaustion.")

    # --- Helper for extraction intent ---
    @staticmethod
    def is_extraction_intent(query: str) -> bool:
        extraction_keywords = ["extract", "full text", "full content", "get document", "show complete text"]
        return any(kw in query.lower() for kw in extraction_keywords)