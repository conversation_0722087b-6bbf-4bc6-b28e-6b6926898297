#!/usr/bin/env python3
"""
Utility functions for indexing documents into the vector database.
Can be used both as a standalone script and as an imported function.
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from src.document_processing.embedding_generator import EmbeddingGenerator
from src.database.vector_store import QdrantVectorStore
from src.config import DATA_DIR, PROCESSED_DIR
from .logger import get_logger

logger = get_logger(__name__)

def index_all_documents() -> bool:
    """Index all processed documents into the vector database."""
    try:
        logger.info("Starting document indexing...")
        
        # Initialize embedding generator
        logger.info("Initializing embedding generator...")
        embedding_generator = EmbeddingGenerator()
        logger.info("✅ Embedding generator initialized")
        
        # Initialize vector store
        logger.info("Initializing vector store...")
        vector_store = QdrantVectorStore()
        logger.info("✅ Vector store initialized")
        
        # Get all chunk files
        chunk_files = list(PROCESSED_DIR.glob("*.chunks.json"))
        logger.info(f"Found {len(chunk_files)} chunk files to index")
        
        total_chunks = 0
        indexed_chunks = 0
        
        for chunk_file in chunk_files:
            logger.info(f"Processing {chunk_file.name}...")
            
            try:
                with open(chunk_file, 'r', encoding='utf-8') as f:
                    chunks = json.load(f)
                
                total_chunks += len(chunks)
                
                # Generate embeddings for all chunks
                texts = [chunk['content'] for chunk in chunks]
                embeddings = embedding_generator.generate_embeddings(texts, is_query=False)
                
                # Prepare points for vector store
                from qdrant_client.models import PointStruct
                from uuid import uuid4
                
                points = []
                for chunk, embedding in zip(chunks, embeddings):
                    point = PointStruct(
                        id=str(uuid4()),
                        vector=embedding.tolist(),
                        payload={
                            'content': chunk['content'],
                            'title': chunk.get('title', ''),
                            'source_file': chunk.get('source_file', ''),
                            'chunk_index': chunk.get('chunk_index', 0),
                            'metadata': chunk.get('metadata', {})
                        }
                    )
                    points.append(point)
                
                # Upsert to vector store
                vector_store.upsert_points(points)
                indexed_chunks += len(chunks)
                logger.info(f"✅ Indexed {len(chunks)} chunks from {chunk_file.name}")
                
            except Exception as e:
                logger.error(f"❌ Error processing {chunk_file.name}: {e}")
                continue
        
        logger.info(f"🎉 Indexing complete! Indexed {indexed_chunks}/{total_chunks} chunks")
        return True
        
    except Exception as e:
        logger.error(f"❌ Indexing failed: {e}")
        return False

def index_specific_document(source_file: str) -> bool:
    """Index a specific document by its source file name."""
    try:
        logger.info(f"Indexing specific document: {source_file}")
        
        # Initialize components
        embedding_generator = EmbeddingGenerator()
        vector_store = QdrantVectorStore()
        
        # Find the chunk file for this document
        chunk_file = PROCESSED_DIR / f"{source_file}.chunks.json"
        
        if not chunk_file.exists():
            logger.error(f"Chunk file not found: {chunk_file}")
            return False
        
        # Load and process chunks
        with open(chunk_file, 'r', encoding='utf-8') as f:
            chunks = json.load(f)
        
        # Generate embeddings
        texts = [chunk['content'] for chunk in chunks]
        embeddings = embedding_generator.generate_embeddings(texts, is_query=False)
        
        # Prepare points
        from qdrant_client.models import PointStruct
        from uuid import uuid4
        
        points = []
        for chunk, embedding in zip(chunks, embeddings):
            point = PointStruct(
                id=str(uuid4()),
                vector=embedding.tolist(),
                payload={
                    'content': chunk['content'],
                    'title': chunk.get('title', ''),
                    'source_file': chunk.get('source_file', ''),
                    'chunk_index': chunk.get('chunk_index', 0),
                    'metadata': chunk.get('metadata', {})
                }
            )
            points.append(point)
        
        # Upsert to vector store
        vector_store.upsert_points(points)
        logger.info(f"✅ Indexed {len(chunks)} chunks from {source_file}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to index {source_file}: {e}")
        return False

def get_indexed_document_count() -> int:
    """Get the number of documents currently indexed in the vector store."""
    try:
        vector_store = QdrantVectorStore()
        # This is a simple count - you might need to adjust based on your vector store implementation
        return vector_store.get_collection_info().points_count
    except Exception as e:
        logger.error(f"Failed to get document count: {e}")
        return 0

if __name__ == "__main__":
    success = index_all_documents()
    if success:
        print("✅ Document indexing completed successfully!")
    else:
        print("❌ Document indexing failed!") 