#!/usr/bin/env python3
"""
Test script for the payroll document processing system.
Demonstrates the complete workflow from document upload to issue detection.
"""

import os
import sys
import tempfile
import json
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.document_processing.payroll_processor import PayrollDocumentProcessor
from src.database.payroll_db import PayrollDatabase
from src.ner.payroll_entity_extractor import PayrollEntityExtractor
from src.document_processing.payroll_classifier import PayrollDocumentClassifier
from src.document_processing.issue_detector import PayrollIssueDetector


def create_sample_payslip_text() -> str:
    """Create a sample payslip text for testing."""
    return """
    TECHCORP PRIVATE LIMITED
    Employee Payslip - July 2025
    
    Employee Details:
    Employee ID: TC1024
    Employee Name: Rohit Verma
    Department: Software Development
    
    Pay Period: July 2025
    Pay Date: 31-07-2025
    
    Earnings:
    Basic Salary: ₹45,000.00
    HRA: ₹18,000.00
    Special Allowance: ₹6,000.00
    Performance Bonus: ₹5,000.00
    Gross Pay: ₹74,000.00
    
    Deductions:
    Income Tax (TDS): ₹3,200.00
    Provident Fund (PF): ₹1,500.00
    ESI: ₹0.00
    Professional Tax: ₹100.00
    Total Deductions: ₹4,800.00
    
    Net Pay: ₹69,200.00
    
    This is a computer-generated payslip.
    """


def create_sample_complaint_text() -> str:
    """Create a sample HR complaint for testing."""
    return """
    I have not received my performance bonus for July 2025. 
    According to my offer letter, I should receive a quarterly bonus 
    based on my performance rating. My manager confirmed that I 
    exceeded expectations this quarter, but the bonus of ₹5,000 
    is missing from my July payslip. Please investigate and 
    process the missing bonus payment.
    """


def test_payroll_processor():
    """Test the complete payroll processing workflow."""
    print("🚀 Testing Payroll Document Processing System")
    print("=" * 60)
    
    # Initialize processor
    print("\n1. Initializing Payroll Processor...")
    try:
        processor = PayrollDocumentProcessor()
        print("✅ Payroll processor initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize processor: {e}")
        return False
    
    # Test database connection
    print("\n2. Testing Database Connection...")
    try:
        stats = processor.get_processing_statistics()
        print(f"✅ Database connected. Current documents: {stats.get('total_documents', 0)}")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    # Test document classification
    print("\n3. Testing Document Classification...")
    try:
        classifier = PayrollDocumentClassifier()
        
        # Train classifier if not already trained
        if not classifier.classifier:
            print("   Training document classifier...")
            classifier.train_classifier()
            print("   ✅ Classifier trained successfully")
        
        # Test classification
        sample_text = create_sample_payslip_text()
        result = classifier.classify_document(sample_text)
        print(f"   ✅ Document classified as: {result.document_type} (confidence: {result.confidence:.3f})")
        
    except Exception as e:
        print(f"   ❌ Classification test failed: {e}")
        return False
    
    # Test entity extraction
    print("\n4. Testing Entity Extraction...")
    try:
        extractor = PayrollEntityExtractor()
        sample_text = create_sample_payslip_text()
        
        extraction_result = extractor.extract_payroll_entities(sample_text)
        structured_data = extraction_result["structured_data"]
        entities = extraction_result["entities"]
        
        print(f"   ✅ Extracted {len(entities)} entities")
        print(f"   📊 Structured data: Employee ID: {structured_data.get('employee_id')}, "
              f"Gross Pay: ₹{structured_data.get('gross_pay')}, Net Pay: ₹{structured_data.get('net_pay')}")
        
    except Exception as e:
        print(f"   ❌ Entity extraction test failed: {e}")
        return False
    
    # Test issue detection
    print("\n5. Testing Issue Detection...")
    try:
        issue_detector = PayrollIssueDetector()
        
        # Test document issues
        document_issues = issue_detector.detect_document_issues(structured_data)
        print(f"   📋 Document issues detected: {len(document_issues)}")
        
        # Test complaint analysis
        complaint_text = create_sample_complaint_text()
        complaint_issues = issue_detector.classify_complaint(complaint_text, structured_data)
        print(f"   📋 Complaint issues detected: {len(complaint_issues)}")
        
        if complaint_issues:
            top_issue = complaint_issues[0]
            print(f"   🔍 Top issue: {top_issue.issue_type} (confidence: {top_issue.confidence:.3f})")
        
    except Exception as e:
        print(f"   ❌ Issue detection test failed: {e}")
        return False
    
    # Test end-to-end processing with a temporary file
    print("\n6. Testing End-to-End Document Processing...")
    try:
        # Create a temporary text file with sample payslip content
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(create_sample_payslip_text())
            temp_path = Path(temp_file.name)
        
        # Process the document
        result = processor.process_document(temp_path, employee_id="TC1024")
        
        # Clean up
        temp_path.unlink()
        
        if result.success:
            print(f"   ✅ Document processed successfully (ID: {result.document_id})")
            print(f"   📊 Document type: {result.document_type}")
            print(f"   ⏱️  Processing time: {result.processing_time:.3f}s")
            print(f"   🎯 Confidence score: {result.confidence_score:.3f}")
            print(f"   📝 Extracted fields: {len(result.extracted_fields or [])}")
            print(f"   ⚠️  Detected issues: {len(result.detected_issues or [])}")
        else:
            print(f"   ❌ Document processing failed: {result.error_message}")
            return False
        
    except Exception as e:
        print(f"   ❌ End-to-end processing test failed: {e}")
        return False
    
    # Test complaint analysis
    print("\n7. Testing Complaint Analysis...")
    try:
        complaint_result = processor.analyze_complaint(
            create_sample_complaint_text(), 
            employee_id="TC1024"
        )
        
        if complaint_result["success"]:
            issues = complaint_result["detected_issues"]
            stats = complaint_result["statistics"]
            
            print(f"   ✅ Complaint analyzed successfully")
            print(f"   📊 Issues detected: {len(issues)}")
            print(f"   ⏱️  Processing time: {complaint_result['processing_time']:.3f}s")
            
            if issues:
                print(f"   🔍 Top issue type: {stats.get('most_common_type', 'N/A')}")
                print(f"   📈 Average confidence: {stats.get('average_confidence', 0):.3f}")
        else:
            print(f"   ❌ Complaint analysis failed: {complaint_result.get('error_message')}")
            return False
        
    except Exception as e:
        print(f"   ❌ Complaint analysis test failed: {e}")
        return False
    
    # Test database queries
    print("\n8. Testing Database Queries...")
    try:
        db = processor.db
        
        # Get documents by employee
        employee_docs = db.get_documents_by_employee("TC1024")
        print(f"   📄 Documents for TC1024: {len(employee_docs)}")
        
        # Get recent statistics
        stats = db.get_statistics()
        print(f"   📊 Total documents in database: {stats.get('total_documents', 0)}")
        print(f"   📈 Processing status breakdown: {stats.get('status_counts', {})}")
        
    except Exception as e:
        print(f"   ❌ Database query test failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 All tests completed successfully!")
    print("\n📋 System Summary:")
    print("   ✅ Document classification working")
    print("   ✅ Entity extraction working")
    print("   ✅ Issue detection working")
    print("   ✅ Database storage working")
    print("   ✅ Complaint analysis working")
    print("   ✅ End-to-end processing working")
    
    return True


def test_individual_components():
    """Test individual components separately."""
    print("\n🔧 Testing Individual Components")
    print("=" * 40)
    
    # Test PayrollEntityExtractor
    print("\n1. Testing PayrollEntityExtractor...")
    try:
        extractor = PayrollEntityExtractor()
        sample_text = "Employee ID: TC1024, Gross Pay: ₹74,000, Net Pay: ₹69,200"
        
        result = extractor.extract_payroll_entities(sample_text)
        entities = result["entities"]
        
        print(f"   ✅ Extracted {len(entities)} entities")
        for entity in entities[:3]:  # Show first 3 entities
            print(f"      - {entity.label}: {entity.text} (confidence: {entity.confidence:.3f})")
        
    except Exception as e:
        print(f"   ❌ PayrollEntityExtractor test failed: {e}")
    
    # Test PayrollDatabase
    print("\n2. Testing PayrollDatabase...")
    try:
        db = PayrollDatabase()
        stats = db.get_statistics()
        print(f"   ✅ Database accessible. Total documents: {stats.get('total_documents', 0)}")
        
    except Exception as e:
        print(f"   ❌ PayrollDatabase test failed: {e}")
    
    # Test PayrollIssueDetector
    print("\n3. Testing PayrollIssueDetector...")
    try:
        detector = PayrollIssueDetector()
        complaint = "My salary is wrong this month. The tax deduction seems incorrect."
        
        issues = detector.classify_complaint(complaint)
        print(f"   ✅ Detected {len(issues)} potential issues")
        
        if issues:
            top_issue = issues[0]
            print(f"      - Top issue: {top_issue.issue_type} (confidence: {top_issue.confidence:.3f})")
        
    except Exception as e:
        print(f"   ❌ PayrollIssueDetector test failed: {e}")


def main():
    """Main test function."""
    print("🧪 Payroll Document Processing System - Test Suite")
    print("=" * 60)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test individual components first
    test_individual_components()
    
    # Test complete system
    success = test_payroll_processor()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! The payroll system is ready for use.")
        print("\n📚 Next Steps:")
        print("   1. Start the FastAPI server: uvicorn app:app --reload")
        print("   2. Access the admin dashboard at: http://localhost:8000/admin")
        print("   3. Use the PayrollManager component to upload documents")
        print("   4. Monitor processing results and detected issues")
    else:
        print("❌ SOME TESTS FAILED! Please check the error messages above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("   2. Check if the database directory exists and is writable")
        print("   3. Verify that the models directory has proper permissions")
    
    print(f"\n📅 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
